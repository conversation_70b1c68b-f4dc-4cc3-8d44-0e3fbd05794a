<?php
// Get study words API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $mode = isset($_GET['mode']) ? $_GET['mode'] : 'random';
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    
    $words = [];
    
    switch ($mode) {
        case 'random':
            // Get random words
            $words = fetchAll("SELECT * FROM words ORDER BY RAND() LIMIT ?", [$limit]);
            break;
            
        case 'new':
            // Get words that haven't been studied
            $words = fetchAll("SELECT * FROM words WHERE times_studied = 0 ORDER BY RAND() LIMIT ?", [$limit]);
            break;
            
        case 'difficult':
            // Get words with low success rate or high forgot count
            $words = fetchAll(
                "SELECT * FROM words WHERE times_studied > 0 AND (success_rate < 50 OR times_forgot > times_remembered) ORDER BY success_rate ASC, RAND() LIMIT ?",
                [$limit]
            );
            break;
            
        case 'review':
            // Get previously studied words
            $words = fetchAll("SELECT * FROM words WHERE times_studied > 0 ORDER BY RAND() LIMIT ?", [$limit]);
            break;
            
        default:
            throw new Exception('شێوازی خوێندن نادروستە');
    }
    
    if (empty($words)) {
        // Fallback to any available words
        $words = fetchAll("SELECT * FROM words ORDER BY RAND() LIMIT ?", [$limit]);
    }
    
    echo json_encode([
        'success' => true,
        'words' => $words,
        'mode' => $mode,
        'count' => count($words)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'هەڵەیەک ڕوویدا لە بەدەستهێنانی وشەکان: ' . $e->getMessage()
    ]);
}
?>

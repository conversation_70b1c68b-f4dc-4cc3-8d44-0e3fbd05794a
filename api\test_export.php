<?php
require_once '../config.php';

try {
    // Check authentication
    if (!isLoggedIn()) {
        http_response_code(401);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => false, 'message' => 'پێویستە بچیتە ژوورەوە']);
        exit;
    }
    
    $userId = getUserId();
    $format = $_GET['format'] ?? 'json';
    
    // Get current user
    $user = getCurrentUser();
    
    // Create test data
    $testData = [
        'export_info' => [
            'exported_at' => date('Y-m-d H:i:s'),
            'user_id' => $userId,
            'user_name' => $user['name'],
            'user_email' => $user['email'],
            'test_export' => true
        ],
        'test_statistics' => [
            'total_words_studied' => 25,
            'total_study_sessions' => 150,
            'words_remembered' => 120,
            'words_forgot' => 30,
            'success_rate' => 80.0
        ],
        'sample_sessions' => [
            [
                'session_date' => '2024-01-15',
                'word' => 'سڵاو',
                'category' => 'گرنگ',
                'result' => 'remembered',
                'response_time_seconds' => 3.5
            ],
            [
                'session_date' => '2024-01-15',
                'word' => 'سوپاس',
                'category' => 'گرنگ',
                'result' => 'remembered',
                'response_time_seconds' => 2.8
            ]
        ]
    ];
    
    if ($format === 'csv') {
        // Export as CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="dastare_test_' . date('Y-m-d') . '.csv"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
        
        $output = fopen('php://output', 'w');
        
        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Export test data
        fputcsv($output, ['Test Export Data']);
        fputcsv($output, ['User', 'Export Date', 'Total Words', 'Success Rate']);
        fputcsv($output, [
            $user['name'],
            date('Y-m-d H:i:s'),
            $testData['test_statistics']['total_words_studied'],
            $testData['test_statistics']['success_rate'] . '%'
        ]);
        
        // Add empty row
        fputcsv($output, []);
        
        // Export sample sessions
        fputcsv($output, ['Sample Study Sessions']);
        fputcsv($output, ['Date', 'Word', 'Category', 'Result', 'Response Time']);
        
        foreach ($testData['sample_sessions'] as $session) {
            fputcsv($output, [
                $session['session_date'],
                $session['word'],
                $session['category'],
                $session['result'],
                $session['response_time_seconds']
            ]);
        }
        
        fclose($output);
        exit;
        
    } else {
        // Export as JSON
        header('Content-Type: application/json; charset=utf-8');
        header('Content-Disposition: attachment; filename="dastare_test_' . date('Y-m-d') . '.json"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
        
        echo json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

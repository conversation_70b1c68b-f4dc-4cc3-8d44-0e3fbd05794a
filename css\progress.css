/* Progress Page Specific Styles */

.overall-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-container {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.chart-container h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.chart-container canvas {
  max-height: 300px;
}

.progress-tables {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.table-container {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.table-container h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.table-wrapper {
  overflow-x: auto;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.progress-table,
.words-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.progress-table th,
.words-table th {
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  padding: 1rem;
  text-align: right;
  border-bottom: 2px solid var(--border-color);
}

.progress-table td,
.words-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.progress-table tr:hover,
.words-table tr:hover {
  background: var(--bg-secondary);
}

.progress-table td.success {
  color: var(--success-color);
  font-weight: 600;
}

.progress-table td.error {
  color: var(--error-color);
  font-weight: 600;
}

.word-cell {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.success-rate {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.85rem;
}

.success-rate.good {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.success-rate.average {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.success-rate.poor {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.practice-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.practice-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.no-data {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: 2rem;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.btn-primary,
.btn-secondary {
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: white;
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

/* Chart Styles */
.chart-container {
  position: relative;
}

/* Loading state for charts */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--text-secondary);
}

.chart-loading i {
  font-size: 2rem;
  margin-bottom: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .overall-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .charts-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chart-container {
    padding: 1.5rem;
  }

  .table-container {
    padding: 1rem;
    overflow-x: hidden;
  }

  .table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin: 0 -1rem;
    padding: 0 1rem;
  }

  .progress-table,
  .words-table {
    min-width: 600px;
    font-size: 0.8rem;
  }

  .progress-table th,
  .words-table th {
    padding: 0.75rem 0.5rem;
    font-size: 0.75rem;
    white-space: nowrap;
  }

  .progress-table td,
  .words-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
    white-space: nowrap;
  }

  .word-cell {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .practice-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
    white-space: nowrap;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .overall-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .chart-container h3 {
    font-size: 1.1rem;
  }

  .table-container h3 {
    font-size: 1.1rem;
  }

  .progress-table th,
  .words-table th {
    font-size: 0.75rem;
  }

  .progress-table td,
  .words-table td {
    font-size: 0.75rem;
    padding: 0.5rem 0.25rem;
  }

  .table-container {
    padding: 0.5rem;
    margin: 0 -0.5rem;
  }

  .table-container h3 {
    padding: 0 0.5rem;
  }

  .table-wrapper {
    margin: 0 -0.5rem;
    padding: 0 0.5rem;
    border-left: none;
    border-right: none;
    border-radius: 0;
  }

  .progress-table,
  .words-table {
    min-width: 500px;
  }

  .word-cell {
    max-width: 100px;
    font-size: 0.8rem;
  }

  .practice-btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .success-rate {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }
}

/* Alternative mobile layout for very small screens */
@media (max-width: 360px) {
  .table-wrapper {
    display: none;
  }

  .mobile-cards {
    display: block;
  }

  .mobile-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
  }

  .mobile-card-header {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1rem;
  }

  .mobile-card-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
  }

  .mobile-card-label {
    color: var(--text-secondary);
    font-weight: 500;
  }

  .mobile-card-value {
    color: var(--text-primary);
  }
}

/* Hide mobile cards by default */
.mobile-cards {
  display: none;
}

/* Print Styles */
@media print {
  .nav,
  .action-buttons,
  .footer {
    display: none;
  }

  .container {
    padding: 0;
    background: white;
  }

  .header {
    background: white;
    box-shadow: none;
    border-bottom: 2px solid #000;
  }

  .stat-card,
  .chart-container,
  .table-container {
    background: white;
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }

  .charts-section {
    display: none; /* Hide charts in print */
  }
}

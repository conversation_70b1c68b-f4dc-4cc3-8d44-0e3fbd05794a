<?php
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // Check authentication
    if (!isLoggedIn()) {
        throw new Exception('Login required');
    }
    
    $language = $_POST['language'] ?? $_GET['language'] ?? null;
    
    if (!$language) {
        throw new Exception('Language parameter required');
    }
    
    // Set the language
    if (setLanguage($language)) {
        $supportedLanguages = getSupportedLanguages();
        $langInfo = $supportedLanguages[$language] ?? null;
        
        if (!$langInfo) {
            throw new Exception('Unsupported language');
        }
        
        echo json_encode([
            'success' => true,
            'language' => $language,
            'name' => $langInfo['name'],
            'direction' => $langInfo['dir'],
            'message' => 'Language changed successfully'
        ]);
    } else {
        throw new Exception('Failed to change language');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

<?php
require_once 'config.php';

// Get progress data
$today = date('Y-m-d');
$todayProgress = fetchRow("SELECT * FROM daily_progress WHERE study_date = ?", [$today]);

if (!$todayProgress) {
    $todayProgress = ['words_studied' => 0, 'words_remembered' => 0, 'words_forgot' => 0, 'success_rate' => 0, 'study_time_minutes' => 0];
}

// Get last 30 days progress
$monthlyProgress = fetchAll("SELECT * FROM daily_progress WHERE study_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) ORDER BY study_date DESC");

// Get last 7 days progress
$weeklyProgress = fetchAll("SELECT * FROM daily_progress WHERE study_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) ORDER BY study_date DESC");

// Calculate totals
$totalWordsStudied = getCount("SELECT SUM(words_studied) FROM daily_progress");
$totalTimeSpent = getCount("SELECT SUM(study_time_minutes) FROM daily_progress");
$averageSuccessRate = fetchRow("SELECT AVG(success_rate) as avg_rate FROM daily_progress WHERE words_studied > 0")['avg_rate'] ?? 0;

// Get most studied words
$topWords = fetchAll("SELECT word, times_studied, success_rate FROM words WHERE times_studied > 0 ORDER BY times_studied DESC LIMIT 10");

// Get words that need practice
$difficultWords = fetchAll("SELECT word, times_studied, success_rate FROM words WHERE times_studied > 0 AND success_rate < 70 ORDER BY success_rate ASC LIMIT 10");
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getLanguageDirection(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('nav_progress'); ?> - <?php echo __('app_title'); ?></title>
    <script>
        // Set initial dark mode before page renders
        (function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.documentElement.classList.add('dark-mode');
                document.body.classList.add('dark-mode');
            }
        })();
    </script>

    <!-- PWA Meta Tags -->
    <meta name="description" content="بینینی پێشکەوتن و ئامارەکانی فێربوون">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دەستەڕێ">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/192.png">
    <link rel="manifest" href="manifest.json">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/progress.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <?php
        $pageSubtitle = __('your_progress');
        include 'includes/header.php';
        ?>

        <!-- Navigation -->
        <nav class="nav">
            <div class="nav-container">
                <a href="index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span><?php echo __('nav_home'); ?></span>
                </a>
                <a href="search.php" class="nav-item">
                    <i class="fas fa-search"></i>
                    <span><?php echo __('nav_search'); ?></span>
                </a>
                <a href="study.php" class="nav-item">
                    <i class="fas fa-graduation-cap"></i>
                    <span><?php echo __('nav_study'); ?></span>
                </a>
                <a href="progress.php" class="nav-item active">
                    <i class="fas fa-chart-line"></i>
                    <span><?php echo __('nav_progress'); ?></span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main">
            <!-- Overall Stats -->
            <div class="overall-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($totalWordsStudied); ?></h3>
                        <p><?php echo __('total_words_studied'); ?></p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($totalTimeSpent); ?></h3>
                        <p><?php echo __('total_study_time'); ?> (<?php echo __('minutes'); ?>)</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($averageSuccessRate, 1); ?>%</h3>
                        <p><?php echo __('overall_success_rate'); ?></p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $todayProgress['words_studied']; ?></h3>
                        <p><?php echo __('studied_today'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <h3>پێشکەوتنی ٧ ڕۆژی ڕابردوو</h3>
                    <canvas id="weeklyChart"></canvas>
                </div>

                <div class="chart-container">
                    <h3>ڕێژەی سەرکەوتن</h3>
                    <canvas id="successChart"></canvas>
                </div>
            </div>

            <!-- Progress Tables -->
            <div class="progress-tables">
                <!-- Recent Progress -->
                <div class="table-container">
                    <h3>پێشکەوتنی دوایی</h3>
                    <div class="table-wrapper">
                        <table class="progress-table">
                            <thead>
                                <tr>
                                    <th>ڕێکەوت</th>
                                    <th>وشەی خوێندراو</th>
                                    <th>بیرکراوە</th>
                                    <th>لەبیرکراوە</th>
                                    <th>سەرکەوتن</th>
                                    <th>کات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($weeklyProgress as $progress): ?>
                                    <tr>
                                        <td><?php echo date('Y/m/d', strtotime($progress['study_date'])); ?></td>
                                        <td><?php echo $progress['words_studied']; ?></td>
                                        <td class="success"><?php echo $progress['words_remembered']; ?></td>
                                        <td class="error"><?php echo $progress['words_forgot']; ?></td>
                                        <td><?php echo number_format($progress['success_rate'], 1); ?>%</td>
                                        <td><?php echo $progress['study_time_minutes']; ?> خولەک</td>
                                    </tr>
                                <?php endforeach; ?>

                                <?php if (empty($weeklyProgress)): ?>
                                    <tr>
                                        <td colspan="6" class="no-data">هیچ زانیارییەک نییە</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Top Words -->
                <div class="table-container">
                    <h3>زۆرترین وشەی خوێندراو</h3>
                    <div class="table-wrapper">
                        <table class="words-table">
                            <thead>
                                <tr>
                                    <th>وشە</th>
                                    <th>جاری خوێندن</th>
                                    <th>سەرکەوتن</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($topWords as $word): ?>
                                    <tr>
                                        <td class="word-cell"><?php echo htmlspecialchars($word['word']); ?></td>
                                        <td><?php echo $word['times_studied']; ?></td>
                                        <td>
                                            <span class="success-rate <?php echo $word['success_rate'] >= 70 ? 'good' : ($word['success_rate'] >= 50 ? 'average' : 'poor'); ?>">
                                                <?php echo number_format($word['success_rate'], 1); ?>%
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                                <?php if (empty($topWords)): ?>
                                    <tr>
                                        <td colspan="3" class="no-data">هیچ وشەیەک نەخوێندراوە</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Difficult Words -->
                <div class="table-container">
                    <h3>وشەکانی پێویستی مەشق</h3>
                    <div class="table-wrapper">
                        <table class="words-table">
                            <thead>
                                <tr>
                                    <th>وشە</th>
                                    <th>جاری خوێندن</th>
                                    <th>سەرکەوتن</th>
                                    <th>کردار</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($difficultWords as $word): ?>
                                    <tr>
                                        <td class="word-cell"><?php echo htmlspecialchars($word['word']); ?></td>
                                        <td><?php echo $word['times_studied']; ?></td>
                                        <td>
                                            <span class="success-rate poor">
                                                <?php echo number_format($word['success_rate'], 1); ?>%
                                            </span>
                                        </td>
                                        <td>
                                            <button class="practice-btn" onclick="practiceWord('<?php echo htmlspecialchars($word['word']); ?>')">
                                                <i class="fas fa-redo"></i>
                                                مەشق
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                                <?php if (empty($difficultWords)): ?>
                                    <tr>
                                        <td colspan="4" class="no-data">هەموو وشەکان باشن!</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="study.php" class="btn-primary">
                    <i class="fas fa-play"></i>
                    دەستپێکردنی خوێندن
                </a>
                <button class="btn-secondary" onclick="exportProgress()">
                    <i class="fas fa-download"></i>
                    دەرهێنانی ئامارەکان
                </button>
                <button class="btn-secondary" onclick="resetProgress()">
                    <i class="fas fa-refresh"></i>
                    ڕیسێتکردنی ئامارەکان
                </button>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; ٢٠٢٥ دەستەڕێ - فێربوونی زمانی ئاماژە</p>
        </footer>
    </div>

    <script>
        // Pass PHP data to JavaScript
        const weeklyData = <?php echo json_encode($weeklyProgress); ?>;
        const monthlyData = <?php echo json_encode($monthlyProgress); ?>;
    </script>
    <script src="js/app.js"></script>
    <script src="js/progress.js"></script>
    <script src="js/shared.js"></script>
    <script>
        // Initialize dark mode on page load
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.body.classList.add('dark-mode');
                document.documentElement.classList.add('dark-mode');
            }
        });
    </script>
</body>
</html>

/* Study Page Specific Styles */

.study-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  color: var(--text-primary);
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.stat-item i {
  color: var(--primary-color);
  font-size: 1.2rem;
}

/* Study Mode Selection */
.study-mode-selection {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.study-mode-selection h2 {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2rem;
}

.mode-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.mode-card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
}

.mode-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.mode-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
}

.mode-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.mode-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Study Session */
.study-session {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.session-info {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.session-progress {
  flex: 1;
  max-width: 300px;
  margin: 0 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  transition: width 0.3s ease;
  width: 0%;
}

.end-session-btn {
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.end-session-btn:hover {
  background: #dc2626;
  transform: translateY(-2px);
}

/* Flashcard */
.flashcard-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.flashcard {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: 3rem 2rem;
  min-height: 400px;
  width: 100%;
  max-width: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
  border: 3px solid var(--primary-color);
}

.flashcard-content {
  text-align: center;
  width: 100%;
}

.word-display h2 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  line-height: 1.2;
}

.video-section {
  margin-bottom: 2rem;
}

.video-section video {
  width: 100%;
  max-width: 400px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
}

.no-video-placeholder {
  padding: 2rem;
  color: var(--text-secondary);
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--border-radius);
  margin: 0 auto;
  max-width: 300px;
}

.no-video-placeholder i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.word-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.word-description,
.word-difficulty {
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
}

/* Study Actions */
.study-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 150px;
  justify-content: center;
}

.action-btn.forgot {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
  color: white;
}

.action-btn.forgot:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.action-btn.remembered {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
}

.action-btn.remembered:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.action-btn.hint {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  color: white;
}

.action-btn.hint:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.action-btn.hint.used {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  opacity: 0.7;
  cursor: not-allowed;
}

.action-btn.hint.used:hover {
  transform: none;
  box-shadow: none;
}

/* Session Stats */
.session-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat {
  text-align: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  min-width: 120px;
}

.stat-label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Session Complete */
.session-complete {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 3rem 2rem;
  box-shadow: var(--shadow-md);
  text-align: center;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.complete-icon {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  color: white;
  font-size: 3rem;
  margin: 0 auto 2rem;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.complete-content h2 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2rem;
}

.final-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.final-stat {
  background: var(--bg-secondary);
  padding: 2rem 1rem;
  border-radius: var(--border-radius);
  text-align: center;
}

.final-stat .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.final-stat .stat-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.complete-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .study-stats {
    gap: 1rem;
  }

  .stat-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .mode-grid {
    grid-template-columns: 1fr;
  }

  .session-header {
    flex-direction: column;
    align-items: stretch;
  }

  .session-progress {
    margin: 1rem 0;
    max-width: none;
  }

  .flashcard {
    padding: 2rem 1rem;
    min-height: 300px;
  }

  .word-display h2 {
    font-size: 2rem;
  }

  .study-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
  }

  .session-stats {
    gap: 1rem;
  }

  .final-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .complete-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .final-stats {
    grid-template-columns: 1fr;
  }

  .complete-content h2 {
    font-size: 1.5rem;
  }

  .complete-icon {
    width: 80px;
    height: 80px;
    font-size: 2rem;
  }
}

<?php
require_once 'config.php';

// Require authentication
requireAuth();

// Get current user
$currentUser = getCurrentUser();

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    $priority = $_POST['priority'] ?? 'medium';
    
    if (!empty($subject) && !empty($message)) {
        // In a real application, you would send an email or save to database
        // For now, we'll just show a success message
        $success = "پەیامەکەت بە سەرکەوتوویی نێردرا. لە ماوەی ٢٤ کاتژمێردا وەڵامت دەدەینەوە.";
    } else {
        $error = "تکایە هەموو خانەکان پڕ بکەرەوە";
    }
}
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getLanguageDirection(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('contact_us'); ?> - <?php echo __('app_title'); ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="js/shared.js"></script>
    <style>
        .contact-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .contact-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .contact-info {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }
        
        .contact-form {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
        }
        
        .contact-icon {
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .contact-details h4 {
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
        }
        
        .contact-details p {
            margin: 0;
            color: var(--text-secondary);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-family: inherit;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .btn-send {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-send:hover {
            background: var(--secondary-color);
        }
        
        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .faq-section {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            margin-top: 2rem;
        }
        
        .faq-item {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
        }
        
        .faq-question {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .faq-answer {
            color: var(--text-secondary);
        }
        
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <?php
        $pageSubtitle = __('contact_us');
        include 'includes/header.php';
        ?>

        <div class="contact-container">
            <div class="contact-header">
                <h1><i class="fas fa-envelope"></i> پەیوەندی</h1>
                <p>ئێمە ئامادەین یارمەتیت بدەین</p>
            </div>
            
            <div class="contact-grid">
                <div class="contact-info">
                    <h3><i class="fas fa-info-circle"></i> زانیاری پەیوەندی</h3>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h4>ئیمەیڵ</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fab fa-telegram"></i>
                        </div>
                        <div class="contact-details">
                            <h4>تێلێگرام</h4>
                            <p>@DastareSupport</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="contact-details">
                            <h4>کاتی وەڵامدانەوە</h4>
                            <p>٢٤ کاتژمێر</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="contact-details">
                            <h4>ماڵپەڕ</h4>
                            <p>www.dastare.com</p>
                        </div>
                    </div>
                </div>
                
                <div class="contact-form">
                    <h3><i class="fas fa-paper-plane"></i> پەیام بنێرە</h3>
                    
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($error)): ?>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="form-group">
                            <label for="subject">بابەت</label>
                            <input type="text" id="subject" name="subject" value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="priority">گرنگی</label>
                            <select id="priority" name="priority">
                                <option value="low" <?php echo ($_POST['priority'] ?? '') === 'low' ? 'selected' : ''; ?>>کەم</option>
                                <option value="medium" <?php echo ($_POST['priority'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>مامناوەند</option>
                                <option value="high" <?php echo ($_POST['priority'] ?? '') === 'high' ? 'selected' : ''; ?>>زۆر</option>
                                <option value="urgent" <?php echo ($_POST['priority'] ?? '') === 'urgent' ? 'selected' : ''; ?>>پەلە</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">پەیام</label>
                            <textarea id="message" name="message" placeholder="پەیامەکەت لێرە بنووسە..." required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn-send">
                            <i class="fas fa-paper-plane"></i>
                            ناردنی پەیام
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="faq-section">
                <h3><i class="fas fa-question-circle"></i> پرسیارە دووبارەکان</h3>
                
                <div class="faq-item">
                    <div class="faq-question">چۆن هەژمارەکەم بسڕمەوە؟</div>
                    <div class="faq-answer">لە پڕۆفایلەکەت، دوگمەی "سڕینەوەی هەژمار" کلیک بکە یان پەیوەندیمان پێوە بکە.</div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">ئایا دەتوانم زانیارییەکانم هەناردە بکەم؟</div>
                    <div class="faq-answer">بەڵێ، لە خوارەوەی پەڕە دوگمەی "هەناردەکردن" کلیک بکە.</div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">چۆن وشەی نوێ زیاد بکەم؟</div>
                    <div class="faq-answer">لە بەشی گەڕان، دوگمەی "زیادکردنی وشەی نوێ" کلیک بکە.</div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">ئایا خزمەتگوزارییەکە بەخۆڕاییە؟</div>
                    <div class="faq-answer">بەڵێ، دەستەڕێ بە تەواوی بەخۆڕاییە و هەمیشە بەخۆڕایی دەمێنێتەوە.</div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i> گەڕانەوە بۆ سەرەکی
                </a>
            </div>
        </div>
    </div>
</body>
</html>

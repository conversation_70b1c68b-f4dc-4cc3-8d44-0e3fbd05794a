<?php
// Edit word API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }
    
    $wordId = isset($_POST['word_id']) ? (int)$_POST['word_id'] : 0;
    $word = isset($_POST['word']) ? trim($_POST['word']) : '';
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $difficulty = isset($_POST['difficulty']) ? $_POST['difficulty'] : 'medium';
    
    if ($wordId <= 0) {
        throw new Exception('ناسنامەی وشە نادروستە');
    }
    
    if (empty($word)) {
        throw new Exception('وشە پێویستە');
    }
    
    // Check if word exists
    $existingWord = fetchRow("SELECT * FROM words WHERE id = ?", [$wordId]);
    if (!$existingWord) {
        throw new Exception('وشە نەدۆزرایەوە');
    }
    
    // Check if another word with the same text exists (excluding current word)
    $duplicateWord = fetchRow("SELECT id FROM words WHERE word = ? AND id != ?", [$word, $wordId]);
    if ($duplicateWord) {
        throw new Exception('وشەیەک بەم ناوەوە پێشتر هەیە');
    }
    
    // Validate difficulty
    if (!in_array($difficulty, ['easy', 'medium', 'hard'])) {
        $difficulty = 'medium';
    }
    
    $conn = getDBConnection();
    
    // Update word basic info
    $stmt = $conn->prepare("UPDATE words SET word = ?, description = ?, difficulty_level = ? WHERE id = ?");
    $stmt->bind_param("sssi", $word, $description, $difficulty, $wordId);
    
    if (!$stmt->execute()) {
        throw new Exception('هەڵەیەک ڕوویدا لە نوێکردنەوەی وشە');
    }
    $stmt->close();
    
    // Handle video upload if provided
    $videoPath = null;
    if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
        $videoPath = handleVideoUpload($_FILES['video'], $wordId, $existingWord['video_path']);
        
        if ($videoPath) {
            // Update word with new video path
            $updateStmt = $conn->prepare("UPDATE words SET video_path = ? WHERE id = ?");
            $updateStmt->bind_param("si", $videoPath, $wordId);
            $updateStmt->execute();
            $updateStmt->close();
        }
    }
    
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'وشە بە سەرکەوتوویی نوێکرایەوە',
        'word_id' => $wordId,
        'video_path' => $videoPath
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function handleVideoUpload($file, $wordId, $oldVideoPath = null) {
    try {
        // Validate file
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('قەبارەی فایل زۆر گەورەیە. حەدی ئەوپەڕ: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
        }
        
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ALLOWED_VIDEO_TYPES)) {
            throw new Exception('جۆری فایل پشتگیری ناکرێت. جۆرە قبوولەکان: ' . implode(', ', ALLOWED_VIDEO_TYPES));
        }
        
        // Generate unique filename
        $filename = 'word_' . $wordId . '_' . time() . '.' . $fileExtension;
        $uploadPath = UPLOAD_DIR . $filename;
        $webPath = UPLOAD_URL . $filename;
        
        // Create upload directory if it doesn't exist
        if (!file_exists(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0777, true);
        }
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            throw new Exception('هەڵەیەک ڕوویدا لە بارکردنی فایل');
        }
        
        // Remove old video if exists
        if ($oldVideoPath) {
            $oldFilePath = UPLOAD_DIR . basename($oldVideoPath);
            if (file_exists($oldFilePath)) {
                unlink($oldFilePath);
            }
        }
        
        return $webPath;
        
    } catch (Exception $e) {
        error_log("Video upload error: " . $e->getMessage());
        throw $e; // Re-throw to be handled by main try-catch
    }
}
?>

<?php
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // Check authentication
    if (!isLoggedIn()) {
        throw new Exception('پێویستە بچیتە ژوورەوە');
    }
    
    $userId = getUserId();
    $action = $_GET['action'] ?? 'get';
    
    if ($action === 'get') {
        // Get notifications for user
        $notifications = generateNotifications($userId);
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'unread_count' => countUnreadNotifications($notifications)
        ]);
        
    } elseif ($action === 'mark_read') {
        // Mark notification as read (store in localStorage on frontend)
        echo json_encode([
            'success' => true,
            'message' => 'ئاگادارکردنەوە وەک خوێندراوە نیشانکرا'
        ]);
        
    } else {
        throw new Exception('کردارێکی نادروست');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function generateNotifications($userId) {
    $notifications = [];
    
    // Get today's progress
    $today = date('Y-m-d');
    $todayProgress = fetchRow("SELECT * FROM daily_progress WHERE user_id = ? AND study_date = ?", [$userId, $today]);
    
    // Get yesterday's progress
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    $yesterdayProgress = fetchRow("SELECT * FROM daily_progress WHERE user_id = ? AND study_date = ?", [$userId, $yesterday]);
    
    // Get recent achievements
    $recentSessions = fetchAll("SELECT * FROM study_sessions WHERE user_id = ? AND session_date >= ? ORDER BY created_at DESC LIMIT 10", [$userId, date('Y-m-d', strtotime('-7 days'))]);
    
    // Achievement: Daily goal completed
    if ($todayProgress && $todayProgress['words_studied'] >= 10) {
        $notifications[] = [
            'id' => 'daily_goal_' . $today,
            'type' => 'achievement',
            'icon' => 'fas fa-trophy',
            'title' => 'پیرۆزبایی!',
            'message' => $todayProgress['words_studied'] . ' وشەی نوێت فێربوو ئەمڕۆ',
            'time' => 'ئەمڕۆ',
            'unread' => !isNotificationRead('daily_goal_' . $today)
        ];
    }
    
    // Study reminder
    if (!$todayProgress || $todayProgress['words_studied'] == 0) {
        $notifications[] = [
            'id' => 'study_reminder_' . $today,
            'type' => 'reminder',
            'icon' => 'fas fa-calendar',
            'title' => 'بیرخستنەوەی خوێندن',
            'message' => 'کاتی خوێندنی ڕۆژانەتە!',
            'time' => 'ئێستا',
            'unread' => true
        ];
    }
    
    // Streak achievement
    $currentStreak = calculateCurrentStreak($userId);
    if ($currentStreak >= 7 && $currentStreak % 7 == 0) {
        $notifications[] = [
            'id' => 'streak_' . $currentStreak,
            'type' => 'achievement',
            'icon' => 'fas fa-fire',
            'title' => 'دەستکەوتی نوێ',
            'message' => 'بەردەوامی ' . $currentStreak . ' ڕۆژت بەدەستهێنا',
            'time' => 'ئەمڕۆ',
            'unread' => !isNotificationRead('streak_' . $currentStreak)
        ];
    }
    
    // Weekly progress summary
    if (date('w') == 0) { // Sunday
        $weeklyWords = getCount("SELECT COUNT(DISTINCT word_id) FROM study_sessions WHERE user_id = ? AND session_date >= ?", [$userId, date('Y-m-d', strtotime('-7 days'))]);
        if ($weeklyWords > 0) {
            $notifications[] = [
                'id' => 'weekly_summary_' . date('Y-W'),
                'type' => 'summary',
                'icon' => 'fas fa-chart-line',
                'title' => 'کورتەی هەفتانە',
                'message' => $weeklyWords . ' وشەی نوێت فێربوو ئەم هەفتەیە',
                'time' => 'ئەم هەفتەیە',
                'unread' => !isNotificationRead('weekly_summary_' . date('Y-W'))
            ];
        }
    }
    
    // Improvement notification
    if ($todayProgress && $yesterdayProgress && $todayProgress['success_rate'] > $yesterdayProgress['success_rate']) {
        $improvement = round($todayProgress['success_rate'] - $yesterdayProgress['success_rate'], 1);
        $notifications[] = [
            'id' => 'improvement_' . $today,
            'type' => 'achievement',
            'icon' => 'fas fa-arrow-up',
            'title' => 'باشتر بوویت!',
            'message' => 'ڕێژەی سەرکەوتنت ' . $improvement . '% باشتر بووە',
            'time' => 'ئەمڕۆ',
            'unread' => !isNotificationRead('improvement_' . $today)
        ];
    }
    
    return $notifications;
}

function calculateCurrentStreak($userId) {
    $today = new DateTime();
    $sessions = fetchAll("SELECT DISTINCT session_date FROM study_sessions WHERE user_id = ? ORDER BY session_date DESC", [$userId]);
    
    $streak = 0;
    $expectedDate = $today;
    
    foreach ($sessions as $session) {
        $sessionDate = new DateTime($session['session_date']);
        $daysDiff = $expectedDate->diff($sessionDate)->days;
        
        if ($daysDiff === 0 || ($daysDiff === 1 && $streak === 0)) {
            $streak++;
            $expectedDate = $sessionDate->sub(new DateInterval('P1D'));
        } else {
            break;
        }
    }
    
    return $streak;
}

function countUnreadNotifications($notifications) {
    return count(array_filter($notifications, function($n) {
        return $n['unread'];
    }));
}

function isNotificationRead($notificationId) {
    // In a real app, this would check a database table
    // For now, we'll assume notifications are unread by default
    // The frontend will handle marking as read using localStorage
    return false;
}
?>

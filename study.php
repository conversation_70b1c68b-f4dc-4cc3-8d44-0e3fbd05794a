<?php
require_once 'config.php';

// Get study statistics
$totalWords = getCount("SELECT COUNT(*) FROM words");
$studiedToday = getCount("SELECT COUNT(DISTINCT word_id) FROM study_sessions WHERE session_date = CURDATE()");
$todayProgress = fetchRow("SELECT * FROM daily_progress WHERE study_date = CURDATE()");

if (!$todayProgress) {
    executeQuery("INSERT INTO daily_progress (study_date) VALUES (CURDATE())");
    $todayProgress = ['words_studied' => 0, 'words_remembered' => 0, 'words_forgot' => 0, 'success_rate' => 0];
}
?>

<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خوێندن - دەستەڕێ</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="فێربوونی وشەکانی زمانی ئاماژەی کوردی">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دەستەڕێ">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/192.png">
    <link rel="manifest" href="manifest.json">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/study.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <img src="assets/images/dastare_logo.png" alt="دەستەڕێ" class="logo-image">
                    <span class="logo-text">دەستەڕێ</span>
                </h1>
                <p class="subtitle">فێربوونی زمانی ئاماژە</p>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="nav">
            <div class="nav-container">
                <a href="index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>سەرەکی</span>
                </a>
                <a href="search.php" class="nav-item">
                    <i class="fas fa-search"></i>
                    <span>گەڕان</span>
                </a>
                <a href="study.php" class="nav-item active">
                    <i class="fas fa-graduation-cap"></i>
                    <span>خوێندن</span>
                </a>
                <a href="progress.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>پێشکەوتن</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main">
            <!-- Study Stats -->
            <div class="study-stats">
                <div class="stat-item">
                    <i class="fas fa-book"></i>
                    <span>کۆی وشەکان: <?php echo number_format($totalWords); ?></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-brain"></i>
                    <span>خوێندراو ئەمڕۆ: <?php echo $studiedToday; ?></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-check-circle"></i>
                    <span>بیرکراوە: <?php echo $todayProgress['words_remembered']; ?></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-percentage"></i>
                    <span>سەرکەوتن: <?php echo number_format($todayProgress['success_rate'], 1); ?>%</span>
                </div>
            </div>

            <!-- Study Mode Selection -->
            <div id="studyModeSelection" class="study-mode-selection">
                <h2>شێوازی خوێندن هەڵبژێرە</h2>
                <div class="mode-grid">
                    <div class="mode-card" onclick="startStudyMode('random')">
                        <div class="mode-icon">
                            <i class="fas fa-random"></i>
                        </div>
                        <h3>وشەی هەڕەمەکی</h3>
                        <p>وشەیەکی هەڕەمەکی لە هەموو وشەکان</p>
                    </div>

                    <div class="mode-card" onclick="startStudyMode('new')">
                        <div class="mode-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3>وشەی نوێ</h3>
                        <p>وشەکانی کە پێشتر نەتخوێندووە</p>
                    </div>

                    <div class="mode-card" onclick="startStudyMode('difficult')">
                        <div class="mode-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <h3>وشەی سەخت</h3>
                        <p>وشەکانی کە زۆرجار لەبیرت کردووە</p>
                    </div>

                    <div class="mode-card" onclick="startStudyMode('review')">
                        <div class="mode-icon">
                            <i class="fas fa-redo"></i>
                        </div>
                        <h3>پێداچوونەوە</h3>
                        <p>وشەکانی کە پێشتر خوێندووتە</p>
                    </div>
                </div>
            </div>

            <!-- Study Session -->
            <div id="studySession" class="study-session" style="display: none;">
                <div class="session-header">
                    <div class="session-info">
                        <span id="currentWordNumber">1</span> لە <span id="totalWordsInSession">10</span>
                    </div>
                    <div class="session-progress">
                        <div class="progress-bar">
                            <div id="progressFill" class="progress-fill"></div>
                        </div>
                    </div>
                    <button class="end-session-btn" onclick="endStudySession()">
                        <i class="fas fa-stop"></i>
                        کۆتایی
                    </button>
                </div>

                <div class="flashcard-container">
                    <div id="flashcard" class="flashcard">
                        <div class="flashcard-content">
                            <div class="word-display">
                                <h2 id="currentWord">وشە</h2>
                            </div>

                            <div id="videoSection" class="video-section">
                                <video id="studyVideo" controls muted style="display: none;">
                                    <source src="" type="video/mp4">
                                    وەشانەکەت پشتگیری ڤیدیۆ ناکات.
                                </video>
                                <div id="noVideoPlaceholder" class="no-video-placeholder">
                                    <i class="fas fa-video-slash"></i>
                                    <p>ڤیدیۆ بۆ ئەم وشەیە نییە</p>
                                </div>
                            </div>

                            <div class="word-info">
                                <div id="wordDescription" class="word-description"></div>
                                <div id="wordDifficulty" class="word-difficulty"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="study-actions">
                    <button id="forgotBtn" class="action-btn forgot" onclick="markWord('forgot')">
                        <i class="fas fa-times"></i>
                        <span>لەبیرم کرد</span>
                    </button>

                    <button id="hintBtn" class="action-btn hint" onclick="showHint()" style="display: none;">
                        <i class="fas fa-lightbulb"></i>
                        <span>ئاماژە</span>
                    </button>

                    <button id="rememberedBtn" class="action-btn remembered" onclick="markWord('remembered')">
                        <i class="fas fa-check"></i>
                        <span>بیرمە</span>
                    </button>
                </div>

                <div class="session-stats">
                    <div class="stat">
                        <span class="stat-label">بیرکراوە:</span>
                        <span id="sessionRemembered" class="stat-value">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">لەبیرکراوە:</span>
                        <span id="sessionForgot" class="stat-value">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">سەرکەوتن:</span>
                        <span id="sessionSuccessRate" class="stat-value">0%</span>
                    </div>
                </div>
            </div>

            <!-- Session Complete -->
            <div id="sessionComplete" class="session-complete" style="display: none;">
                <div class="complete-content">
                    <div class="complete-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h2>ئافەرین! کۆتایی هات بە خوێندنەکەت</h2>

                    <div class="final-stats">
                        <div class="final-stat">
                            <div class="stat-number" id="finalWordsStudied">0</div>
                            <div class="stat-label">وشەی خوێندراو</div>
                        </div>
                        <div class="final-stat">
                            <div class="stat-number" id="finalRemembered">0</div>
                            <div class="stat-label">بیرکراوە</div>
                        </div>
                        <div class="final-stat">
                            <div class="stat-number" id="finalSuccessRate">0%</div>
                            <div class="stat-label">سەرکەوتن</div>
                        </div>
                        <div class="final-stat">
                            <div class="stat-number" id="finalHintsUsed">0</div>
                            <div class="stat-label">ئاماژە بەکارهێنراو</div>
                        </div>
                        <div class="final-stat">
                            <div class="stat-number" id="finalTimeSpent">0</div>
                            <div class="stat-label">خولەک</div>
                        </div>
                    </div>

                    <div class="complete-actions">
                        <button class="btn-primary" onclick="startNewSession()">
                            <i class="fas fa-redo"></i>
                            خوێندنی نوێ
                        </button>
                        <button class="btn-secondary" onclick="goToProgress()">
                            <i class="fas fa-chart-line"></i>
                            بینینی پێشکەوتن
                        </button>
                        <button class="btn-secondary" onclick="goHome()">
                            <i class="fas fa-home"></i>
                            گەڕانەوە بۆ سەرەکی
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; ٢٠٢٥ دەستەڕێ - فێربوونی زمانی ئاماژە</p>
        </footer>
    </div>

    <script src="js/app.js"></script>
    <script src="js/study.js"></script>
</body>
</html>

<?php
// Search API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $query = isset($_GET['q']) ? trim($_GET['q']) : '';
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    
    if ($query) {
        // Search for words containing the query
        $words = fetchAll(
            "SELECT * FROM words WHERE word LIKE ? ORDER BY word LIMIT ? OFFSET ?",
            ["%$query%", $limit, $offset]
        );
        
        $total = getCount(
            "SELECT COUNT(*) FROM words WHERE word LIKE ?",
            ["%$query%"]
        );
    } else {
        // Get all words
        $words = fetchAll(
            "SELECT * FROM words ORDER BY word LIMIT ? OFFSET ?",
            [$limit, $offset]
        );
        
        $total = getCount("SELECT COUNT(*) FROM words");
    }
    
    echo json_encode([
        'success' => true,
        'words' => $words,
        'total' => $total,
        'query' => $query
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'هەڵەیەک ڕوویدا لە گەڕان: ' . $e->getMessage()
    ]);
}
?>

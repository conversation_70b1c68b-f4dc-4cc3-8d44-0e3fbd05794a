<?php
// Delete word API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $wordId = isset($input['id']) ? (int)$input['id'] : 0;

    if ($wordId <= 0) {
        throw new Exception('ناسنامەی وشە نادروستە');
    }

    // Get word details before deletion
    $word = fetchRow("SELECT * FROM words WHERE id = ?", [$wordId]);
    if (!$word) {
        throw new Exception('وشە نەدۆزرایەوە');
    }

    $conn = getDBConnection();

    // Delete associated video file if exists
    if ($word['video_path']) {
        $filePath = UPLOAD_DIR . basename($word['video_path']);
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }

    // Delete study sessions (will be handled by foreign key constraint)
    // Delete word
    $stmt = $conn->prepare("DELETE FROM words WHERE id = ?");
    $stmt->bind_param("i", $wordId);

    if (!$stmt->execute()) {
        throw new Exception('هەڵەیەک ڕوویدا لە سڕینەوەی وشە');
    }

    $stmt->close();
    $conn->close();

    echo json_encode([
        'success' => true,
        'message' => 'وشە بە سەرکەوتوویی سڕایەوە'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

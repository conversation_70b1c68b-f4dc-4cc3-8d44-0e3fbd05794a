<?php
// Database configuration for Sign Language Learning App
// XAMPP MySQL Configuration

define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'dastare');

// Include authentication configuration
require_once __DIR__ . '/auth_config.php';

// Include language system
require_once __DIR__ . '/includes/language.php';

// Create database connection
function getDBConnection() {
    try {
        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);

        // Check connection
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }

        // Set charset to UTF-8 for Kurdish text support
        $conn->set_charset("utf8mb4");

        return $conn;
    } catch (Exception $e) {
        die("Database connection error: " . $e->getMessage());
    }
}

// Helper function to execute queries safely
function executeQuery($query, $params = []) {
    $conn = getDBConnection();

    if (!empty($params)) {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }

        // Create types string for bind_param
        $types = str_repeat('s', count($params));
        $stmt->bind_param($types, ...$params);

        $result = $stmt->execute();

        if (!$result) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $stmt->close();
    } else {
        $result = $conn->query($query);
        if (!$result) {
            throw new Exception("Query failed: " . $conn->error);
        }
    }

    $conn->close();
    return $result;
}

// Helper function to get single row
function fetchRow($query, $params = []) {
    $result = executeQuery($query, $params);
    return $result->fetch_assoc();
}

// Helper function to get all rows
function fetchAll($query, $params = []) {
    $result = executeQuery($query, $params);
    $rows = [];
    while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
    }
    return $rows;
}

// Helper function to get count
function getCount($query, $params = []) {
    $result = executeQuery($query, $params);
    $row = $result->fetch_row();
    return $row[0];
}

// Application settings
define('UPLOAD_DIR', __DIR__ . '/uploads/videos/');
define('UPLOAD_URL', 'uploads/videos/'); // Web-accessible path
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_VIDEO_TYPES', ['mp4', 'webm', 'ogg', 'mov', 'avi']);

// Create uploads directory if it doesn't exist
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0777, true);
}

// Helper function to convert file path to web URL
function getVideoUrl($filePath) {
    if (empty($filePath)) {
        return null;
    }

    // If it's already a web path, return as is
    if (strpos($filePath, 'uploads/') === 0) {
        return $filePath;
    }

    // Convert absolute path to relative web path
    $basePath = __DIR__ . '/';
    if (strpos($filePath, $basePath) === 0) {
        return substr($filePath, strlen($basePath));
    }

    return $filePath;
}

// Authentication functions
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getUserId() {
    return $_SESSION['user_id'] ?? null;
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    $userId = getUserId();
    return fetchRow("SELECT * FROM users WHERE id = ?", [$userId]);
}

function requireAuth() {
    if (!isLoggedIn()) {
        // For demo purposes, create a test user session
        $_SESSION['user_id'] = 1;
        $_SESSION['user_name'] = 'بەکارهێنەری تاقیکردنەوە';
        $_SESSION['user_email'] = '<EMAIL>';

        // Try to create user in database if not exists
        try {
            $existingUser = fetchRow("SELECT * FROM users WHERE id = 1");
            if (!$existingUser) {
                executeQuery("INSERT INTO users (id, name, email, created_at) VALUES (?, ?, ?, NOW())",
                    [1, 'بەکارهێنەری تاقیکردنەوە', '<EMAIL>']);
            }
        } catch (Exception $e) {
            // Ignore database errors for demo
        }
    }
}

// Session configuration
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set timezone
date_default_timezone_set('Asia/Baghdad');

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>

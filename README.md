# دەستەڕێ - فێربوونی زمانی ئاماژە

# Dastare - Kurdish Sign Language Learning Platform

A modern, responsive web application for learning Kurdish Sign Language with flash card system, progress tracking, and video support.

## Features

### 🔍 Search & Browse

- Search through Kurdish words with real-time results
- View word details with video demonstrations
- Add new words with video uploads
- Edit and manage existing words

### 📚 Study System

- Flash card learning system
- Multiple study modes:
  - Random words
  - New words (never studied)
  - Difficult words (low success rate)
  - Review mode (previously studied)
- "Remembered" and "Forgot" tracking
- Keyboard shortcuts for efficient studying

### 📊 Progress Tracking

- Daily progress statistics
- Weekly and monthly charts
- Success rate tracking
- Study time monitoring
- Words that need practice identification

### 🎥 Video Support

- Upload and manage sign language videos
- HTML5 video player with controls
- Support for multiple video formats (MP4, WebM, OGG, MOV, AVI)

### 🌐 Kurdish Language Support

- Full Kurdish Sorani interface
- RTL (Right-to-Left) text support
- Kurdish date formatting
- Responsive design for all devices

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Charts**: Chart.js
- **Icons**: Font Awesome 6
- **Fonts**: Noto Sans Arabic (for Kurdish support)

## Installation

### Prerequisites

- XAMPP (or similar LAMP/WAMP stack)
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser with modern JavaScript support

### Setup Instructions

1. **Download and Install XAMPP**

   - Download from [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Install and start Apache and MySQL services

2. **Setup Database**

   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Create a new database named `dastare`
   - Import the database schema by running the SQL commands in `database.sql`

3. **Deploy Application**

   - Copy all project files to `C:\xampp\htdocs\dastare\`
   - Ensure the `uploads/videos/` directory has write permissions

4. **Configure Database Connection**

   - Open `config.php`
   - Verify database settings (default: localhost, root, no password)
   - Modify if your setup is different

5. **Access Application**
   - Open your web browser
   - Navigate to `http://localhost/dastare/`
   - The application should load with the dashboard

## Database Schema

### Tables

1. **words** - Stores vocabulary words

   - `id` - Primary key
   - `word` - Kurdish word text
   - `video_path` - Path to sign language video
   - `description` - Optional word description
   - `difficulty_level` - easy/medium/hard
   - `times_studied` - Number of times studied
   - `times_remembered` - Number of times remembered
   - `times_forgot` - Number of times forgotten
   - `success_rate` - Calculated success percentage

2. **daily_progress** - Daily study statistics

   - `id` - Primary key
   - `study_date` - Date of study session
   - `words_studied` - Number of words studied
   - `words_remembered` - Number remembered
   - `words_forgot` - Number forgotten
   - `success_rate` - Daily success rate
   - `study_time_minutes` - Time spent studying

3. **study_sessions** - Individual study records
   - `id` - Primary key
   - `word_id` - Foreign key to words table
   - `result` - remembered/forgot
   - `response_time_seconds` - Time taken to respond
   - `session_date` - Date of study session

## Usage Guide

### Adding Words

1. Go to the Search page
2. Click "زیادکردنی وشەی نوێ" (Add New Word)
3. Fill in the word details
4. Optionally upload a sign language video
5. Click "زیادکردن" (Add)

### Studying

1. Go to the Study page
2. Choose a study mode:
   - **Random**: Random words from database
   - **New**: Words never studied before
   - **Difficult**: Words with low success rate
   - **Review**: Previously studied words
3. For each word, click:
   - "بیرمە" (I Remember) if you know the sign
   - "لەبیرم کرد" (I Forgot) if you don't know
4. View your session results at the end

### Viewing Progress

1. Go to the Progress page
2. View daily statistics and charts
3. See words that need more practice
4. Export progress data if needed

### Keyboard Shortcuts

- **During Study**:
  - `→` or `2`: Mark as remembered
  - `←` or `1`: Mark as forgot
  - `Space`: Toggle video playback
  - `Esc`: End study session
- **Global**:
  - `Ctrl+K`: Focus search input
  - `Esc`: Close modals

## File Structure

```
dastare/
├── index.php              # Main dashboard
├── search.php             # Search and word management
├── study.php              # Flash card study system
├── progress.php           # Progress tracking and charts
├── config.php             # Database configuration
├── database.sql           # Database schema and initial data
├── README.md              # This file
├── css/
│   ├── style.css          # Main styles
│   ├── search.css         # Search page styles
│   ├── study.css          # Study page styles
│   └── progress.css       # Progress page styles
├── js/
│   ├── app.js             # Main application JavaScript
│   ├── search.js          # Search functionality
│   ├── study.js           # Study system logic
│   └── progress.js        # Progress charts and data
├── api/
│   ├── search.php         # Search API endpoint
│   ├── get_word.php       # Get single word data
│   ├── add_word.php       # Add new word
│   ├── upload_video.php   # Upload video files
│   ├── delete_word.php    # Delete word
│   ├── get_study_words.php # Get words for study
│   ├── record_study.php   # Record study results
│   ├── update_progress.php # Update daily progress
│   └── reset_progress.php # Reset all progress
└── uploads/
    └── videos/            # Uploaded video files
```

## Customization

### Adding More Words

- Import your CSV file data into the `words` table
- Or use the web interface to add words manually

### Changing Appearance

- Modify CSS variables in `css/style.css` for colors and styling
- Update fonts and layout as needed

### Database Configuration

- Modify `config.php` for different database settings
- Update upload limits and allowed file types

## Troubleshooting

### Common Issues

1. **Database Connection Error**

   - Check XAMPP MySQL service is running
   - Verify database name and credentials in `config.php`

2. **Video Upload Issues**

   - Ensure `uploads/videos/` directory exists and is writable
   - Check file size limits in PHP configuration
   - Verify video file format is supported

3. **Kurdish Text Display Issues**

   - Ensure browser supports Arabic/Kurdish fonts
   - Check that UTF-8 encoding is properly set

4. **JavaScript Errors**
   - Check browser console for errors
   - Ensure all JavaScript files are loaded correctly

## Contributing

This is an educational project for Kurdish Sign Language learning. Contributions are welcome:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For support or questions about this Kurdish Sign Language learning platform, please create an issue in the project repository.

---

**ئەمازە** - فێربوونی زمانی ئاماژەی کوردی بە شێوەیەکی مۆدێرن و بەکارهێنەر دۆست

<?php
// Test upload functionality
require_once 'config.php';

echo "<h2>Upload Directory Test</h2>";

echo "<p><strong>Upload Directory:</strong> " . UPLOAD_DIR . "</p>";
echo "<p><strong>Upload URL:</strong> " . UPLOAD_URL . "</p>";

// Check if directory exists
if (file_exists(UPLOAD_DIR)) {
    echo "<p style='color: green;'>✓ Upload directory exists</p>";
    
    // Check if writable
    if (is_writable(UPLOAD_DIR)) {
        echo "<p style='color: green;'>✓ Upload directory is writable</p>";
    } else {
        echo "<p style='color: red;'>✗ Upload directory is not writable</p>";
    }
    
    // List files in directory
    $files = scandir(UPLOAD_DIR);
    $videoFiles = array_filter($files, function($file) {
        return !in_array($file, ['.', '..']);
    });
    
    echo "<p><strong>Files in upload directory:</strong></p>";
    if (empty($videoFiles)) {
        echo "<p>No files found</p>";
    } else {
        echo "<ul>";
        foreach ($videoFiles as $file) {
            $webPath = UPLOAD_URL . $file;
            echo "<li><a href='$webPath' target='_blank'>$file</a></li>";
        }
        echo "</ul>";
    }
    
} else {
    echo "<p style='color: red;'>✗ Upload directory does not exist</p>";
    
    // Try to create it
    if (mkdir(UPLOAD_DIR, 0777, true)) {
        echo "<p style='color: green;'>✓ Created upload directory</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create upload directory</p>";
    }
}

// Test database connection
echo "<h3>Database Test</h3>";
try {
    $conn = getDBConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check words with videos
    $wordsWithVideos = fetchAll("SELECT id, word, video_path FROM words WHERE video_path IS NOT NULL AND video_path != '' LIMIT 5");
    
    if (!empty($wordsWithVideos)) {
        echo "<p><strong>Words with videos:</strong></p>";
        echo "<ul>";
        foreach ($wordsWithVideos as $word) {
            $videoUrl = getVideoUrl($word['video_path']);
            echo "<li>{$word['word']} - <a href='$videoUrl' target='_blank'>{$word['video_path']}</a></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No words with videos found</p>";
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test video URL function
echo "<h3>Video URL Function Test</h3>";
$testPaths = [
    'uploads/videos/test.mp4',
    '/full/path/uploads/videos/test.mp4',
    UPLOAD_DIR . 'test.mp4'
];

foreach ($testPaths as $path) {
    $url = getVideoUrl($path);
    echo "<p>Path: <code>$path</code> → URL: <code>$url</code></p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
</style>

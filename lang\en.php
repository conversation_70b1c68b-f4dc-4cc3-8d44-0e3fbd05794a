<?php
// English Language File
return [
    // Common
    'welcome' => 'Welcome',
    'home' => 'Home',
    'search' => 'Search',
    'study' => 'Study',
    'progress' => 'Progress',
    'profile' => 'Profile',
    'help' => 'Help',
    'settings' => 'Settings',
    'notifications' => 'Notifications',
    'logout' => 'Logout',
    'login' => 'Login',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'add' => 'Add',
    'close' => 'Close',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'loading' => 'Loading...',
    'error' => 'Error',
    'success' => 'Success',
    'warning' => 'Warning',
    'info' => 'Info',
    
    // Header
    'app_title' => 'Dastare',
    'app_subtitle' => 'Sign Language Learning',
    'user_greeting' => 'Hello, %s',
    
    // Navigation
    'nav_home' => 'Home',
    'nav_search' => 'Search',
    'nav_study' => 'Study',
    'nav_progress' => 'Progress',
    
    // Settings
    'dark_mode' => 'Dark Mode',
    'auto_play' => 'Auto-play Videos',
    'study_reminder' => 'Study Reminder',
    'notifications_enabled' => 'Notifications Enabled',
    'language' => 'Language',
    
    // Export
    'export' => 'Export',
    'export_data' => 'Export Data',
    'export_format' => 'Choose export format:\nOK = JSON\nCancel = CSV',
    'export_starting' => 'Starting data export...',
    'export_success' => 'Data exported successfully',
    'export_error' => 'Error occurred during export',
    
    // Authentication
    'logout_confirm' => 'Are you sure you want to logout?',
    'login_required' => 'Login required',
    
    // Notifications
    'no_notifications' => 'No notifications',
    'mark_as_read' => 'Mark as read',
    
    // Toast messages
    'dark_mode_enabled' => 'Dark mode enabled',
    'dark_mode_disabled' => 'Light mode enabled',
    'language_changed' => 'Language changed to %s',
    'settings_saved' => 'Settings saved',
    
    // Profile
    'profile_title' => 'User Profile',
    'personal_info' => 'Personal Information',
    'statistics' => 'Statistics',
    'account_settings' => 'Account Settings',
    'member_since' => 'Member since',
    'total_words' => 'Total Words',
    'study_sessions' => 'Study Sessions',
    'success_rate' => 'Success Rate',
    'current_streak' => 'Current Streak',
    'longest_streak' => 'Longest Streak',
    
    // Help
    'help_title' => 'Help & Guidance',
    'help_subtitle' => 'Everything you need to use Dastare',
    'getting_started' => 'How do I get started?',
    'how_to_learn' => 'How do I learn?',
    'faq' => 'Frequently Asked Questions',
    'contact_info' => 'Contact Information',
    
    // Legal pages
    'privacy_policy' => 'Privacy Policy',
    'terms_of_service' => 'Terms of Service',
    'contact_us' => 'Contact Us',
    
    // Footer
    'all_rights_reserved' => 'All rights reserved',
    'version' => 'Version',
    
    // Search
    'search_words' => 'Search Words',
    'add_new_word' => 'Add New Word',
    'no_results' => 'No results found',
    'search_placeholder' => 'Type a word...',
    
    // Study
    'start_studying' => 'Start Studying',
    'remember' => 'Remember',
    'forgot' => 'Forgot',
    'next_word' => 'Next Word',
    'study_complete' => 'Study Complete',
    
    // Time
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'this_week' => 'This Week',
    'this_month' => 'This Month',
    'days_ago' => '%d days ago',
    'hours_ago' => '%d hours ago',
    'minutes_ago' => '%d minutes ago',
    
    // Errors
    'page_not_found' => 'Page Not Found',
    'access_denied' => 'Access Denied',
    'server_error' => 'Server Error',
    'network_error' => 'Network Error',
    'try_again' => 'Try Again',
];
?>

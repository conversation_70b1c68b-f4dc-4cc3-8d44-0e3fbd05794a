// Search Page JavaScript
// Handle word search, modal interactions, and CRUD operations

let currentWordId = null;
let searchTimeout = null;
let currentQuery = "";
let currentOffset = 0;
let isLoading = false;
let hasMoreResults = true;
const WORDS_PER_PAGE = 20;

// Initialize search page
document.addEventListener("DOMContentLoaded", function () {
  initializeSearchPage();
});

function initializeSearchPage() {
  setupSearchHandlers();
  setupModalHandlers();
  setupFormHandlers();
  setupInfiniteScroll();

  // Initialize with current page state
  initializePageState();
}

// Setup search functionality
function setupSearchHandlers() {
  const searchInput = document.querySelector(".search-input");
  if (searchInput) {
    // Real-time search with debounce
    searchInput.addEventListener("input", debounceSearch);

    // Handle enter key
    searchInput.addEventListener("keydown", function (event) {
      if (event.key === "Enter") {
        event.preventDefault();
        performSearch(null, true); // true indicates this is a new search
      }
    });
  }
}

// Debounced search function
const debounceSearch = DastareApp.debounce(function (event) {
  const query = event.target.value.trim();
  if (query.length >= 2 || query.length === 0) {
    performSearch(query, true); // true indicates this is a new search
  }
}, 500);

// Perform search
async function performSearch(query = null, isNewSearch = false) {
  if (query === null) {
    const searchInput = document.querySelector(".search-input");
    query = searchInput ? searchInput.value.trim() : "";
  }

  // If this is a new search, reset pagination
  if (isNewSearch) {
    currentQuery = query;
    currentOffset = 0;
    hasMoreResults = true;
  }

  // Prevent multiple simultaneous requests
  if (isLoading) return;
  isLoading = true;

  try {
    DastareApp.showLoading(true);

    const response = await fetch(
      `api/search.php?q=${encodeURIComponent(
        query
      )}&limit=${WORDS_PER_PAGE}&offset=${currentOffset}`
    );
    const data = await response.json();

    if (data.success) {
      updateSearchResults(data.words, query, isNewSearch);
      updateSearchInfo(data.total, query);

      // Update pagination state
      currentOffset += WORDS_PER_PAGE;
      hasMoreResults =
        data.words.length === WORDS_PER_PAGE && currentOffset < data.total;
    } else {
      DastareApp.showNotification(
        data.message || "هەڵەیەک ڕوویدا لە گەڕان",
        "error"
      );
    }
  } catch (error) {
    console.error("Search error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە گەڕان", "error");
  } finally {
    DastareApp.showLoading(false);
    isLoading = false;
  }
}

// Update search results
function updateSearchResults(words, query, isNewSearch = false) {
  const wordsGrid = document.querySelector(".words-grid");
  const noResults = document.querySelector(".no-results");
  const wordsSection = document.querySelector(".words-section");

  if (!wordsGrid || !wordsSection) return;

  if (words.length === 0 && isNewSearch) {
    // Only show no results for new searches, not for load more
    wordsGrid.style.display = "none";
    if (!noResults) {
      const noResultsDiv = document.createElement("div");
      noResultsDiv.className = "no-results";
      noResultsDiv.innerHTML = `
                <i class="fas fa-search"></i>
                <h3>هیچ وشەیەک نەدۆزرایەوە</h3>
                <p>تکایە وشەیەکی تر تاقی بکەرەوە</p>
            `;
      wordsSection.appendChild(noResultsDiv);
    } else {
      noResults.style.display = "block";
    }
  } else if (words.length > 0) {
    if (noResults) noResults.style.display = "none";
    wordsGrid.style.display = "grid";

    if (isNewSearch) {
      // Replace all results for new search
      wordsGrid.innerHTML = words.map((word) => createWordCard(word)).join("");
    } else {
      // Append results for load more
      const newCards = words.map((word) => createWordCard(word)).join("");
      wordsGrid.insertAdjacentHTML("beforeend", newCards);
    }

    // Remove loading indicator if it exists
    removeLoadingIndicator();
  }
}

// Create word card HTML
function createWordCard(word) {
  return `
        <div class="word-card" onclick="openWordModal(${word.id})">
            <div class="word-content">
                <h3 class="word-text">${escapeHtml(word.word)}</h3>
                ${
                  word.times_studied > 0
                    ? `
                    <div class="word-meta">
                        <span class="study-count">
                            <i class="fas fa-brain"></i>
                            ${word.times_studied} جار خوێندراوە
                        </span>
                    </div>
                `
                    : ""
                }
            </div>
        </div>
    `;
}

// Update search info
function updateSearchInfo(total, query) {
  const searchInfo = document.querySelector(".search-info p");
  if (searchInfo) {
    if (query) {
      searchInfo.textContent = `${total} وشە دۆزرایەوە بۆ "${query}"`;
    } else {
      searchInfo.textContent = `کۆی وشەکان: ${DastareApp.formatNumber(total)}`;
    }
  }
}

// Initialize page state for infinite scroll
function initializePageState() {
  const searchInput = document.querySelector(".search-input");
  const wordsGrid = document.querySelector(".words-grid");

  if (searchInput) {
    currentQuery = searchInput.value.trim();
  }

  // If there are already words on the page, set up pagination state
  if (wordsGrid && wordsGrid.children.length > 0) {
    currentOffset = wordsGrid.children.length;
    hasMoreResults = true; // Assume there might be more results
  }
}

// Setup infinite scroll
function setupInfiniteScroll() {
  const wordsSection = document.querySelector(".words-section");
  if (!wordsSection) return;

  // Create intersection observer for infinite scroll
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && hasMoreResults && !isLoading) {
          loadMoreWords();
        }
      });
    },
    {
      root: null,
      rootMargin: "100px", // Start loading 100px before reaching the bottom
      threshold: 0.1,
    }
  );

  // Create a sentinel element at the bottom
  const sentinel = document.createElement("div");
  sentinel.id = "scroll-sentinel";
  sentinel.style.height = "1px";
  sentinel.style.width = "100%";
  wordsSection.appendChild(sentinel);

  observer.observe(sentinel);
}

// Load more words (infinite scroll)
async function loadMoreWords() {
  if (!hasMoreResults || isLoading) return;

  // Show loading indicator
  showLoadingIndicator();

  // Load more words with current query
  await performSearch(currentQuery, false);
}

// Show loading indicator for infinite scroll
function showLoadingIndicator() {
  const wordsSection = document.querySelector(".words-section");
  if (!wordsSection) return;

  // Remove existing loading indicator
  removeLoadingIndicator();

  const loadingDiv = document.createElement("div");
  loadingDiv.id = "infinite-loading";
  loadingDiv.className = "infinite-loading";
  loadingDiv.innerHTML = `
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <span>بارکردنی وشەکانی زیاتر...</span>
    </div>
  `;

  // Insert before the sentinel
  const sentinel = document.getElementById("scroll-sentinel");
  if (sentinel) {
    wordsSection.insertBefore(loadingDiv, sentinel);
  } else {
    wordsSection.appendChild(loadingDiv);
  }
}

// Remove loading indicator
function removeLoadingIndicator() {
  const loadingDiv = document.getElementById("infinite-loading");
  if (loadingDiv) {
    loadingDiv.remove();
  }
}

// Modal Functions

// Setup modal handlers
function setupModalHandlers() {
  // Close modals when clicking outside
  document.addEventListener("click", function (event) {
    if (event.target.classList.contains("modal")) {
      DastareApp.closeAllModals();
    }
  });
}

// Open word modal
async function openWordModal(wordId) {
  currentWordId = wordId;

  try {
    DastareApp.showLoading(true);

    const response = await fetch(`api/get_word.php?id=${wordId}`);
    const data = await response.json();

    if (data.success) {
      populateWordModal(data.word);
      showModal("wordModal");
    } else {
      DastareApp.showNotification(data.message || "هەڵەیەک ڕوویدا", "error");
    }
  } catch (error) {
    console.error("Error loading word:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە بارکردنی وشە", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Populate word modal with data
function populateWordModal(word) {
  document.getElementById("modalWordText").textContent = word.word;

  // Handle video
  const video = document.getElementById("wordVideo");
  const noVideoMessage = document.getElementById("noVideoMessage");

  if (word.video_path) {
    video.src = word.video_path;
    video.muted = true; // Ensure video is muted
    video.volume = 0; // Set volume to 0
    video.classList.add("force-muted"); // Add force-muted class
    video.style.display = "block";
    noVideoMessage.style.display = "none";
  } else {
    video.style.display = "none";
    noVideoMessage.style.display = "block";
  }

  // Update word details
  document.getElementById("wordDescription").textContent =
    word.description || "-";
  document.getElementById("wordDifficulty").textContent = getDifficultyText(
    word.difficulty_level
  );
  document.getElementById("wordStudyCount").textContent =
    word.times_studied || "0";
  document.getElementById("wordSuccessRate").textContent = word.success_rate
    ? `${word.success_rate}%`
    : "0%";
}

// Get difficulty text in Kurdish
function getDifficultyText(level) {
  const levels = {
    easy: "ئاسان",
    medium: "مامناوەند",
    hard: "سەخت",
  };
  return levels[level] || "مامناوەند";
}

// Close word modal
function closeWordModal() {
  hideModal("wordModal");
  currentWordId = null;

  // Reset video
  const video = document.getElementById("wordVideo");
  if (video) {
    video.pause();
    video.currentTime = 0;
  }

  hideVideoUpload();
}

// Show video upload section
function showVideoUpload() {
  document.getElementById("videoUploadSection").style.display = "block";
  document.getElementById("uploadWordId").value = currentWordId;
}

// Hide video upload section
function hideVideoUpload() {
  document.getElementById("videoUploadSection").style.display = "none";
  const form = document.getElementById("videoUploadForm");
  if (form) form.reset();

  // Clean up recording interface for modal
  cleanupVideoRecording("modal");
}

// Open add word modal
function openAddWordModal() {
  showModal("addWordModal");
}

// Close add word modal
function closeAddWordModal() {
  hideModal("addWordModal");
  const form = document.getElementById("addWordForm");
  if (form) form.reset();
}

// Show modal
function showModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.style.display = "flex";
    modal.classList.add("active");
    document.body.style.overflow = "hidden";
  }
}

// Hide modal
function hideModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.remove("active");
    modal.style.display = "none";
    document.body.style.overflow = "";
  }
}

// CRUD Operations

// Setup form handlers
function setupFormHandlers() {
  // Video upload form
  const videoUploadForm = document.getElementById("videoUploadForm");
  if (videoUploadForm) {
    videoUploadForm.addEventListener("submit", handleVideoUpload);
  }

  // Add word form
  const addWordForm = document.getElementById("addWordForm");
  if (addWordForm) {
    addWordForm.addEventListener("submit", handleAddWord);
  }

  // Edit word form
  const editWordForm = document.getElementById("editWordForm");
  if (editWordForm) {
    editWordForm.addEventListener("submit", handleEditWord);
  }
}

// Handle video upload
async function handleVideoUpload(event) {
  event.preventDefault();

  const formData = new FormData(event.target);

  // Check if there's a recorded video in modal
  const recordedVideo = document.getElementById("modalRecordedPreview");
  if (recordedVideo && recordedVideo.recordedBlob) {
    // Add recorded video to form data
    formData.set("video", recordedVideo.recordedBlob, "recorded_video.webm");
  }

  // Check if there's a processed (cropped) video file
  const videoInput = document.getElementById("videoFile");
  if (videoInput && videoInput.processedFile) {
    formData.set("video", videoInput.processedFile);
  }

  try {
    DastareApp.showLoading(true);

    const response = await fetch("api/upload_video.php", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();

    if (data.success) {
      DastareApp.showNotification("ڤیدیۆ بە سەرکەوتوویی زیادکرا", "success");
      hideVideoUpload();

      // Refresh word modal
      if (currentWordId) {
        openWordModal(currentWordId);
      }

      // Refresh search results
      performSearch();
    } else {
      DastareApp.showNotification(
        data.message || "هەڵەیەک ڕوویدا لە بارکردنی ڤیدیۆ",
        "error"
      );
    }
  } catch (error) {
    console.error("Video upload error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە بارکردنی ڤیدیۆ", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Handle add word
async function handleAddWord(event) {
  event.preventDefault();

  const formData = new FormData(event.target);

  // Check if there's a recorded video
  const recordedVideo = document.getElementById("addRecordedPreview");
  if (recordedVideo && recordedVideo.recordedBlob) {
    // Add recorded video to form data
    formData.set("video", recordedVideo.recordedBlob, "recorded_video.webm");
  }

  // Check if there's a processed (cropped) video file
  const videoInput = document.getElementById("newVideo");
  if (videoInput && videoInput.processedFile) {
    formData.set("video", videoInput.processedFile);
  }

  try {
    DastareApp.showLoading(true);

    const response = await fetch("api/add_word.php", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();

    if (data.success) {
      DastareApp.showNotification("وشە بە سەرکەوتوویی زیادکرا", "success");
      closeAddWordModal();

      // Refresh search results
      performSearch();
    } else {
      DastareApp.showNotification(
        data.message || "هەڵەیەک ڕوویدا لە زیادکردنی وشە",
        "error"
      );
    }
  } catch (error) {
    console.error("Add word error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە زیادکردنی وشە", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Edit word
async function editWord(wordId) {
  try {
    DastareApp.showLoading(true);

    const response = await fetch(`api/get_word.php?id=${wordId}`);
    const data = await response.json();

    if (data.success) {
      populateEditModal(data.word);
      showModal("editWordModal");
    } else {
      DastareApp.showNotification(data.message || "هەڵەیەک ڕوویدا", "error");
    }
  } catch (error) {
    console.error("Error loading word for edit:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە بارکردنی وشە", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Edit current word in modal
function editCurrentWord() {
  if (currentWordId) {
    editWord(currentWordId);
  }
}

// Populate edit modal with word data
function populateEditModal(word) {
  document.getElementById("editWordId").value = word.id;
  document.getElementById("editWord").value = word.word;
  document.getElementById("editDescription").value = word.description || "";
  document.getElementById("editDifficulty").value =
    word.difficulty_level || "medium";

  // Handle current video display
  const currentVideo = document.getElementById("currentEditVideo");
  const noCurrentVideo = document.getElementById("noCurrentVideo");
  const videoActions = document.getElementById("videoActions");

  if (word.video_path) {
    currentVideo.src = word.video_path;
    currentVideo.muted = true; // Ensure video is muted
    currentVideo.volume = 0; // Set volume to 0
    currentVideo.classList.add("force-muted"); // Add force-muted class
    currentVideo.style.display = "block";
    noCurrentVideo.style.display = "none";
    videoActions.style.display = "flex";
  } else {
    currentVideo.style.display = "none";
    noCurrentVideo.style.display = "block";
    videoActions.style.display = "none";
  }
}

// Close edit word modal
function closeEditWordModal() {
  hideModal("editWordModal");
  const form = document.getElementById("editWordForm");
  if (form) form.reset();

  // Reset video
  const video = document.getElementById("currentEditVideo");
  if (video) {
    video.pause();
    video.currentTime = 0;
    video.src = "";
  }

  // Reset video actions
  const videoActions = document.getElementById("videoActions");
  if (videoActions) {
    videoActions.style.display = "none";
  }

  // Clean up recording interface
  cleanupVideoRecording("edit");
}

// Close add word modal
function closeAddWordModal() {
  hideModal("addWordModal");
  const form = document.getElementById("addWordForm");
  if (form) form.reset();

  // Clean up recording interface
  cleanupVideoRecording("add");
}

// Clean up video recording interface
function cleanupVideoRecording(mode) {
  // Stop camera stream
  if (currentStream) {
    currentStream.getTracks().forEach((track) => track.stop());
    currentStream = null;
  }

  // Hide video sections
  hideAllVideoSections(mode);

  // Reset button states
  const buttons = document.querySelectorAll(
    `#${mode}WordModal .video-option-btn`
  );
  buttons.forEach((btn) => btn.classList.remove("active"));

  // Clean up recorded video
  const recordedVideo = document.getElementById(`${mode}RecordedPreview`);
  if (recordedVideo && recordedVideo.src) {
    URL.revokeObjectURL(recordedVideo.src);
    recordedVideo.src = "";
    recordedVideo.recordedBlob = null;
  }

  // Reset recording controls
  const startBtn = document.getElementById(`${mode}StartRecord`);
  const stopBtn = document.getElementById(`${mode}StopRecord`);
  const retakeBtn = document.getElementById(`${mode}RetakeRecord`);

  if (startBtn) {
    startBtn.style.display = "block";
    startBtn.classList.remove("recording");
  }
  if (stopBtn) stopBtn.style.display = "none";
  if (retakeBtn) retakeBtn.style.display = "none";

  // Reset status
  updateRecordingStatus(mode, "ئامادەیی بۆ تۆمارکردن");
}

// Handle edit word form submission
async function handleEditWord(event) {
  event.preventDefault();

  const formData = new FormData(event.target);

  // Check if there's a recorded video
  const recordedVideo = document.getElementById("editRecordedPreview");
  if (recordedVideo && recordedVideo.recordedBlob) {
    // Add recorded video to form data
    formData.set("video", recordedVideo.recordedBlob, "recorded_video.webm");
  }

  // Check if there's a processed (cropped) video file
  const videoInput = document.getElementById("editVideo");
  if (videoInput && videoInput.processedFile) {
    formData.set("video", videoInput.processedFile);
  }

  try {
    DastareApp.showLoading(true);

    const response = await fetch("api/edit_word.php", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();

    if (data.success) {
      DastareApp.showNotification("وشە بە سەرکەوتوویی نوێکرایەوە", "success");
      closeEditWordModal();

      // Refresh search results
      performSearch();

      // If word modal is open, refresh it
      if (currentWordId && currentWordId == formData.get("word_id")) {
        openWordModal(currentWordId);
      }
    } else {
      DastareApp.showNotification(
        data.message || "هەڵەیەک ڕوویدا لە نوێکردنەوەی وشە",
        "error"
      );
    }
  } catch (error) {
    console.error("Edit word error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە نوێکردنەوەی وشە", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Delete word from modal
async function deleteCurrentWordFromModal() {
  if (!currentWordId) {
    DastareApp.showNotification("هیچ وشەیەک هەڵنەبژێردراوە", "error");
    return;
  }

  if (!confirm("دڵنیای لە سڕینەوەی ئەم وشەیە؟")) {
    return;
  }

  try {
    DastareApp.showLoading(true);

    const response = await fetch("api/delete_word.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ id: currentWordId }),
    });

    const data = await response.json();

    if (data.success) {
      DastareApp.showNotification("وشە بە سەرکەوتوویی سڕایەوە", "success");

      // Close the modal
      closeWordModal();

      // Refresh search results
      performSearch();
    } else {
      DastareApp.showNotification(
        data.message || "هەڵەیەک ڕوویدا لە سڕینەوەی وشە",
        "error"
      );
    }
  } catch (error) {
    console.error("Delete word error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە سڕینەوەی وشە", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Delete word (legacy function - kept for compatibility)
async function deleteWord(wordId) {
  if (!confirm("دڵنیای لە سڕینەوەی ئەم وشەیە؟")) {
    return;
  }

  try {
    DastareApp.showLoading(true);

    const response = await fetch("api/delete_word.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ id: wordId }),
    });

    const data = await response.json();

    if (data.success) {
      DastareApp.showNotification("وشە بە سەرکەوتوویی سڕایەوە", "success");

      // Refresh search results
      performSearch();
    } else {
      DastareApp.showNotification(
        data.message || "هەڵەیەک ڕوویدا لە سڕینەوەی وشە",
        "error"
      );
    }
  } catch (error) {
    console.error("Delete word error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە سڕینەوەی وشە", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Delete current video from word
async function deleteCurrentVideo() {
  const wordId = document.getElementById("editWordId").value;

  if (!confirm("دڵنیای لە سڕینەوەی ڤیدیۆکە؟")) {
    return;
  }

  try {
    DastareApp.showLoading(true);

    const response = await fetch("api/delete_video.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ word_id: wordId }),
    });

    const data = await response.json();

    if (data.success) {
      DastareApp.showNotification("ڤیدیۆ بە سەرکەوتوویی سڕایەوە", "success");

      // Update the edit modal to show no video
      const currentVideo = document.getElementById("currentEditVideo");
      const noCurrentVideo = document.getElementById("noCurrentVideo");
      const videoActions = document.getElementById("videoActions");

      currentVideo.style.display = "none";
      currentVideo.src = "";
      noCurrentVideo.style.display = "block";
      videoActions.style.display = "none";

      // Refresh search results
      performSearch();

      // If word modal is open, refresh it
      if (currentWordId && currentWordId == wordId) {
        openWordModal(currentWordId);
      }
    } else {
      DastareApp.showNotification(
        data.message || "هەڵەیەک ڕوویدا لە سڕینەوەی ڤیدیۆ",
        "error"
      );
    }
  } catch (error) {
    console.error("Delete video error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە سڕینەوەی ڤیدیۆ", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Video recording functionality
let mediaRecorder = null;
let recordedChunks = [];
let currentStream = null;
let currentMode = null; // 'add' or 'edit'

// Show video recording interface
async function showRecordVideo(mode) {
  currentMode = mode;
  hideAllVideoSections(mode);

  const recordingSection = document.getElementById(`${mode}VideoRecording`);
  recordingSection.style.display = "block";

  // Update button states
  updateVideoOptionButtons(mode, "record");

  try {
    // Request camera access with square aspect ratio
    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        width: { ideal: 720 },
        height: { ideal: 720 },
        aspectRatio: 1.0,
        facingMode: "user",
      },
      audio: true,
    });

    currentStream = stream;
    const preview = document.getElementById(`${mode}CameraPreview`);
    preview.srcObject = stream;

    // Ensure the preview is flipped to show true orientation
    preview.style.transform = "scaleX(-1)";

    updateRecordingStatus(mode, "ئامادەیی بۆ تۆمارکردن");
  } catch (error) {
    console.error("Camera access error:", error);
    DastareApp.showNotification("دەستگەیشتن بە کامێرا نەبوو", "error");
    updateRecordingStatus(mode, "کامێرا بەردەست نییە");
  }
}

// Show file selection interface
function showSelectVideo(mode) {
  currentMode = mode;
  hideAllVideoSections(mode);

  const selectionSection = document.getElementById(`${mode}VideoSelection`);
  selectionSection.style.display = "block";

  // Update button states
  updateVideoOptionButtons(mode, "select");
}

// Hide all video input sections
function hideAllVideoSections(mode) {
  document.getElementById(`${mode}VideoRecording`).style.display = "none";
  document.getElementById(`${mode}VideoSelection`).style.display = "none";

  // Stop any existing stream
  if (currentStream) {
    currentStream.getTracks().forEach((track) => track.stop());
    currentStream = null;
  }
}

// Update video option button states
function updateVideoOptionButtons(mode, activeOption) {
  let selector;
  if (mode === "modal") {
    selector = "#videoUploadSection .video-option-btn";
  } else {
    selector = `#${mode}WordModal .video-option-btn`;
  }

  const buttons = document.querySelectorAll(selector);
  buttons.forEach((btn) => btn.classList.remove("active"));

  if (activeOption === "record") {
    buttons[0].classList.add("active");
  } else if (activeOption === "select") {
    buttons[1].classList.add("active");
  }
}

// Start recording
async function startRecording(mode) {
  if (!currentStream) {
    DastareApp.showNotification("کامێرا ئامادە نییە", "error");
    return;
  }

  try {
    recordedChunks = [];

    mediaRecorder = new MediaRecorder(currentStream, {
      mimeType: "video/webm;codecs=vp9",
    });

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunks.push(event.data);
      }
    };

    mediaRecorder.onstop = async () => {
      const blob = new Blob(recordedChunks, { type: "video/webm" });

      // Flip the recorded video to match the preview orientation
      try {
        const flippedBlob = await flipVideoHorizontally(blob);
        showRecordedVideo(mode, flippedBlob);
      } catch (error) {
        console.error("Error flipping video:", error);
        // Fallback to original video if flipping fails
        showRecordedVideo(mode, blob);
      }
    };

    mediaRecorder.start();

    // Update UI
    document.getElementById(`${mode}StartRecord`).style.display = "none";
    document.getElementById(`${mode}StopRecord`).style.display = "block";
    document.getElementById(`${mode}StartRecord`).classList.add("recording");

    updateRecordingStatus(mode, "تۆمارکردن...", true);
  } catch (error) {
    console.error("Recording start error:", error);
    DastareApp.showNotification("تۆمارکردن دەست پێ نەکرد", "error");
  }
}

// Stop recording
function stopRecording(mode) {
  if (mediaRecorder && mediaRecorder.state === "recording") {
    mediaRecorder.stop();

    // Update UI - hide stop button and remove recording class
    document.getElementById(`${mode}StopRecord`).style.display = "none";
    document.getElementById(`${mode}StartRecord`).classList.remove("recording");

    updateRecordingStatus(mode, "تۆمارکردن تەواو بوو");

    // Note: showRecordedVideo() will handle showing only the retake button
  }
}

// Show recorded video
function showRecordedVideo(mode, blob) {
  const preview = document.getElementById(`${mode}CameraPreview`);
  const recorded = document.getElementById(`${mode}RecordedPreview`);

  preview.style.display = "none";
  recorded.style.display = "block";
  recorded.src = URL.createObjectURL(blob);
  recorded.muted = true; // Ensure recorded video is muted
  recorded.volume = 0; // Set volume to 0
  recorded.classList.add("force-muted"); // Add force-muted class

  // Store the blob for form submission
  recorded.recordedBlob = blob;

  // Hide start button, show only retake button
  const startBtn = document.getElementById(`${mode}StartRecord`);
  const retakeBtn = document.getElementById(`${mode}RetakeRecord`);

  if (startBtn) startBtn.style.display = "none";
  if (retakeBtn) retakeBtn.style.display = "block";
}

// Retake recording
function retakeRecording(mode) {
  const preview = document.getElementById(`${mode}CameraPreview`);
  const recorded = document.getElementById(`${mode}RecordedPreview`);

  recorded.style.display = "none";
  preview.style.display = "block";

  // Show start button again, hide retake button
  const startBtn = document.getElementById(`${mode}StartRecord`);
  const retakeBtn = document.getElementById(`${mode}RetakeRecord`);

  if (startBtn) startBtn.style.display = "block";
  if (retakeBtn) retakeBtn.style.display = "none";

  updateRecordingStatus(mode, "ئامادەیی بۆ تۆمارکردن");

  // Clean up recorded video
  if (recorded.src) {
    URL.revokeObjectURL(recorded.src);
    recorded.src = "";
    recorded.recordedBlob = null;
  }
}

// Update recording status
function updateRecordingStatus(mode, message, isRecording = false) {
  const status = document.getElementById(`${mode}RecordingStatus`);
  status.textContent = message;

  if (isRecording) {
    status.classList.add("recording");
  } else {
    status.classList.remove("recording");
  }
}

// Video cropping functionality
let currentVideoFile = null;
let cropData = null;

// Handle video file selection
function handleVideoFileSelect(event, mode) {
  const file = event.target.files[0];
  if (!file) return;

  currentVideoFile = file;

  // Create video element to check dimensions
  const video = document.createElement("video");
  video.src = URL.createObjectURL(file);

  video.onloadedmetadata = () => {
    const { videoWidth, videoHeight } = video;

    // Check if video is already square (or close to square)
    const aspectRatio = videoWidth / videoHeight;
    const isSquare = Math.abs(aspectRatio - 1) < 0.1; // Allow 10% tolerance

    if (isSquare) {
      // Video is already square, no need to crop
      DastareApp.showNotification("ڤیدیۆ پێشتر چوارگۆشەیە", "info");
      // Proceed with upload
      processVideoFile(file, mode);
    } else {
      // Video needs cropping
      showVideoCropper(file, mode, videoWidth, videoHeight);
    }

    // Clean up
    URL.revokeObjectURL(video.src);
  };
}

// Show video cropping interface
function showVideoCropper(file, mode, videoWidth, videoHeight) {
  const cropperSection = document.getElementById(`${mode}VideoCropper`);
  const cropperVideo = document.getElementById(`${mode}CropperVideo`);
  const cropSelection = document.getElementById(`${mode}CropSelection`);

  // Show cropper interface
  cropperSection.style.display = "block";

  // Set video source
  cropperVideo.src = URL.createObjectURL(file);
  cropperVideo.muted = true; // Ensure cropper video is muted
  cropperVideo.volume = 0; // Set volume to 0
  cropperVideo.classList.add("force-muted"); // Add force-muted class

  // Calculate initial crop area (perfect square - 1:1 ratio)
  const minDimension = Math.min(videoWidth, videoHeight);
  const cropSize = minDimension * 0.8; // 80% of minimum dimension for better framing
  const cropX = (videoWidth - cropSize) / 2;
  const cropY = (videoHeight - cropSize) / 2;

  // Store crop data - ensure perfect 1:1 ratio
  cropData = {
    x: cropX,
    y: cropY,
    width: cropSize,
    height: cropSize, // Always equal to width for perfect 1:1 ratio
    videoWidth,
    videoHeight,
  };

  // Position crop selection overlay
  cropperVideo.onloadedmetadata = () => {
    updateCropSelection(mode);
    setupCropInteraction(mode);
  };
}

// Update crop selection visual
function updateCropSelection(mode) {
  const cropperVideo = document.getElementById(`${mode}CropperVideo`);
  const cropSelection = document.getElementById(`${mode}CropSelection`);

  const videoRect = cropperVideo.getBoundingClientRect();

  // Calculate how the video is displayed (with object-fit: contain behavior)
  const videoAspectRatio = cropData.videoWidth / cropData.videoHeight;
  const containerAspectRatio = videoRect.width / videoRect.height;

  let actualVideoWidth,
    actualVideoHeight,
    offsetX = 0,
    offsetY = 0;

  if (videoAspectRatio > containerAspectRatio) {
    // Video is wider - fits to container width, letterboxed top/bottom
    actualVideoWidth = videoRect.width;
    actualVideoHeight = videoRect.width / videoAspectRatio;
    offsetY = (videoRect.height - actualVideoHeight) / 2;
  } else {
    // Video is taller - fits to container height, pillarboxed left/right
    actualVideoHeight = videoRect.height;
    actualVideoWidth = videoRect.height * videoAspectRatio;
    offsetX = (videoRect.width - actualVideoWidth) / 2;
  }

  // Calculate scale based on actual displayed video size
  const scaleX = actualVideoWidth / cropData.videoWidth;
  const scaleY = actualVideoHeight / cropData.videoHeight;

  // Calculate display position and size relative to actual video display
  const displayX = offsetX + cropData.x * scaleX;
  const displayY = offsetY + cropData.y * scaleY;
  const displayWidth = cropData.width * scaleX;
  const displayHeight = cropData.height * scaleY;

  // Position the crop selection accurately
  cropSelection.style.left = displayX + "px";
  cropSelection.style.top = displayY + "px";
  cropSelection.style.width = displayWidth + "px";
  cropSelection.style.height = displayHeight + "px";
}

// Setup crop interaction (drag to move and resize)
function setupCropInteraction(mode) {
  const cropSelection = document.getElementById(`${mode}CropSelection`);
  const cropperVideo = document.getElementById(`${mode}CropperVideo`);
  const resizeHandles = cropSelection.querySelectorAll(".resize-handle");

  let isDragging = false;
  let isResizing = false;
  let resizeDirection = null;
  let startX, startY, startCropX, startCropY, startCropWidth, startCropHeight;

  // Handle dragging (moving the crop area)
  cropSelection.addEventListener("mousedown", (e) => {
    if (e.target.classList.contains("resize-handle")) return; // Don't drag when clicking resize handle

    isDragging = true;
    startX = e.clientX;
    startY = e.clientY;
    startCropX = cropData.x;
    startCropY = cropData.y;
    e.preventDefault();
  });

  // Handle resize handles
  resizeHandles.forEach((handle) => {
    handle.addEventListener("mousedown", (e) => {
      isResizing = true;
      resizeDirection = handle.classList[1]; // resize-se, resize-sw, etc.
      startX = e.clientX;
      startY = e.clientY;
      startCropX = cropData.x;
      startCropY = cropData.y;
      startCropWidth = cropData.width;
      startCropHeight = cropData.height;
      e.preventDefault();
      e.stopPropagation();
    });
  });

  document.addEventListener("mousemove", (e) => {
    if (!isDragging && !isResizing) return;

    const videoRect = cropperVideo.getBoundingClientRect();

    // Calculate actual video display dimensions and offsets
    const videoAspectRatio = cropData.videoWidth / cropData.videoHeight;
    const containerAspectRatio = videoRect.width / videoRect.height;

    let actualVideoWidth,
      actualVideoHeight,
      offsetX = 0,
      offsetY = 0;

    if (videoAspectRatio > containerAspectRatio) {
      // Video is wider - fits to container width, letterboxed top/bottom
      actualVideoWidth = videoRect.width;
      actualVideoHeight = videoRect.width / videoAspectRatio;
      offsetY = (videoRect.height - actualVideoHeight) / 2;
    } else {
      // Video is taller - fits to container height, pillarboxed left/right
      actualVideoHeight = videoRect.height;
      actualVideoWidth = videoRect.height * videoAspectRatio;
      offsetX = (videoRect.width - actualVideoWidth) / 2;
    }

    // Calculate scale based on actual displayed video size
    const scaleX = cropData.videoWidth / actualVideoWidth;
    const scaleY = cropData.videoHeight / actualVideoHeight;

    const deltaX = (e.clientX - startX) * scaleX;
    const deltaY = (e.clientY - startY) * scaleY;

    if (isDragging) {
      // Move the crop area
      const newX = Math.max(
        0,
        Math.min(startCropX + deltaX, cropData.videoWidth - cropData.width)
      );
      const newY = Math.max(
        0,
        Math.min(startCropY + deltaY, cropData.videoHeight - cropData.height)
      );

      cropData.x = newX;
      cropData.y = newY;
    } else if (isResizing) {
      // Resize the crop area (maintain square ratio)
      let newWidth = startCropWidth;
      let newHeight = startCropHeight;
      let newX = startCropX;
      let newY = startCropY;

      // Calculate size change based on resize direction
      if (resizeDirection.includes("e")) {
        // East (right)
        newWidth = Math.max(50, startCropWidth + deltaX);
      }
      if (resizeDirection.includes("w")) {
        // West (left)
        newWidth = Math.max(50, startCropWidth - deltaX);
        newX = startCropX + (startCropWidth - newWidth);
      }
      if (resizeDirection.includes("s")) {
        // South (bottom)
        newHeight = Math.max(50, startCropHeight + deltaY);
      }
      if (resizeDirection.includes("n")) {
        // North (top)
        newHeight = Math.max(50, startCropHeight - deltaY);
        newY = startCropY + (startCropHeight - newHeight);
      }

      // Force perfect square ratio - use the average of width and height changes
      const avgDelta = (Math.abs(deltaX) + Math.abs(deltaY)) / 2;
      let squareSize;

      // Determine resize direction and calculate square size
      if (resizeDirection.includes("e") && resizeDirection.includes("s")) {
        // Southeast: use the larger delta for more responsive feel
        squareSize = Math.max(50, startCropWidth + Math.max(deltaX, deltaY));
      } else if (
        resizeDirection.includes("w") &&
        resizeDirection.includes("s")
      ) {
        // Southwest: use the larger delta
        squareSize = Math.max(50, startCropWidth + Math.max(-deltaX, deltaY));
      } else if (
        resizeDirection.includes("e") &&
        resizeDirection.includes("n")
      ) {
        // Northeast: use the larger delta
        squareSize = Math.max(50, startCropWidth + Math.max(deltaX, -deltaY));
      } else if (
        resizeDirection.includes("w") &&
        resizeDirection.includes("n")
      ) {
        // Northwest: use the larger delta
        squareSize = Math.max(50, startCropWidth + Math.max(-deltaX, -deltaY));
      }

      // Adjust position based on resize direction to maintain square from correct corner
      if (resizeDirection === "resize-se") {
        // Southeast: grow from top-left (no position change)
        newWidth = squareSize;
        newHeight = squareSize;
        newX = startCropX;
        newY = startCropY;
      } else if (resizeDirection === "resize-sw") {
        // Southwest: grow from top-right
        newWidth = squareSize;
        newHeight = squareSize;
        newX = startCropX + startCropWidth - squareSize;
        newY = startCropY;
      } else if (resizeDirection === "resize-ne") {
        // Northeast: grow from bottom-left
        newWidth = squareSize;
        newHeight = squareSize;
        newX = startCropX;
        newY = startCropY + startCropHeight - squareSize;
      } else if (resizeDirection === "resize-nw") {
        // Northwest: grow from bottom-right
        newWidth = squareSize;
        newHeight = squareSize;
        newX = startCropX + startCropWidth - squareSize;
        newY = startCropY + startCropHeight - squareSize;
      }

      // Bounds checking - ensure square stays within video boundaries
      newX = Math.max(0, Math.min(newX, cropData.videoWidth - squareSize));
      newY = Math.max(0, Math.min(newY, cropData.videoHeight - squareSize));

      // Final boundary check - adjust square size if needed
      const maxWidth = cropData.videoWidth - newX;
      const maxHeight = cropData.videoHeight - newY;
      const finalSquareSize = Math.min(squareSize, maxWidth, maxHeight);

      // Update crop data with perfect square dimensions
      cropData.x = newX;
      cropData.y = newY;
      cropData.width = finalSquareSize;
      cropData.height = finalSquareSize; // Always equal to width for perfect 1:1 ratio
    }

    updateCropSelection(mode);
  });

  document.addEventListener("mouseup", () => {
    isDragging = false;
    isResizing = false;
    resizeDirection = null;
  });
}

// Apply crop and process video
async function applyCrop(mode) {
  if (!currentVideoFile || !cropData) {
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە بڕینی ڤیدیۆ", "error");
    return;
  }

  try {
    DastareApp.showLoading(true);

    // Create cropped video using canvas and MediaRecorder
    const croppedBlob = await cropVideoToSquare(currentVideoFile, cropData);

    // Process the cropped video
    processVideoFile(croppedBlob, mode);

    // Hide cropper
    hideCropper(mode);

    DastareApp.showNotification("ڤیدیۆ بە سەرکەوتوویی بڕا", "success");
  } catch (error) {
    console.error("Crop error:", error);
    DastareApp.showNotification("هەڵەیەک ڕوویدا لە بڕینی ڤیدیۆ", "error");
  } finally {
    DastareApp.showLoading(false);
  }
}

// Cancel crop
function cancelCrop(mode) {
  hideCropper(mode);

  // Reset file input
  const fileInput = document.getElementById(
    mode === "modal" ? "videoFile" : `${mode}Video`
  );
  if (fileInput) fileInput.value = "";

  currentVideoFile = null;
  cropData = null;
}

// Hide cropper interface
function hideCropper(mode) {
  const cropperSection = document.getElementById(`${mode}VideoCropper`);
  const cropperVideo = document.getElementById(`${mode}CropperVideo`);

  cropperSection.style.display = "none";

  if (cropperVideo.src) {
    URL.revokeObjectURL(cropperVideo.src);
    cropperVideo.src = "";
  }
}

// Process video file (either original or cropped)
function processVideoFile(file, mode) {
  // Convert blob to File if needed
  let processedFile = file;
  if (file instanceof Blob && !(file instanceof File)) {
    processedFile = new File([file], "cropped_video.webm", {
      type: "video/webm",
      lastModified: Date.now(),
    });
  }

  // Store the processed file for form submission
  try {
    if (mode === "add") {
      const input = document.getElementById("newVideo");
      const dt = new DataTransfer();
      dt.items.add(processedFile);
      input.files = dt.files;
    } else if (mode === "edit") {
      const input = document.getElementById("editVideo");
      const dt = new DataTransfer();
      dt.items.add(processedFile);
      input.files = dt.files;
    } else if (mode === "modal") {
      const input = document.getElementById("videoFile");
      const dt = new DataTransfer();
      dt.items.add(processedFile);
      input.files = dt.files;
    }
  } catch (error) {
    console.error("Error setting file input:", error);
    // Fallback: store file reference for manual form submission
    if (mode === "add") {
      document.getElementById("newVideo").processedFile = processedFile;
    } else if (mode === "edit") {
      document.getElementById("editVideo").processedFile = processedFile;
    } else if (mode === "modal") {
      document.getElementById("videoFile").processedFile = processedFile;
    }
  }
}

// Crop video to square using canvas and MediaRecorder
async function cropVideoToSquare(videoFile, cropData) {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    // Set canvas to perfect square dimensions (1:1 ratio)
    const outputSize = Math.min(cropData.width, cropData.height);
    canvas.width = outputSize;
    canvas.height = outputSize; // Always equal to width for perfect 1:1 ratio

    video.src = URL.createObjectURL(videoFile);

    video.onloadedmetadata = () => {
      const stream = canvas.captureStream(30); // 30 FPS
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "video/webm;codecs=vp9",
      });

      const chunks = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: "video/webm" });
        URL.revokeObjectURL(video.src);
        resolve(blob);
      };

      // Start recording
      mediaRecorder.start();
      video.play();

      // Draw frames
      const drawFrame = () => {
        if (video.ended || video.paused) {
          mediaRecorder.stop();
          return;
        }

        // Draw cropped frame to canvas
        ctx.drawImage(
          video,
          cropData.x,
          cropData.y,
          cropData.width,
          cropData.height,
          0,
          0,
          outputSize,
          outputSize
        );

        requestAnimationFrame(drawFrame);
      };

      video.addEventListener("play", drawFrame);
    };

    video.onerror = () => {
      reject(new Error("Video loading failed"));
    };
  });
}

// Flip video horizontally to correct mirrored recording
async function flipVideoHorizontally(videoBlob) {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    video.src = URL.createObjectURL(videoBlob);
    video.muted = true; // Ensure it's muted

    video.onloadedmetadata = () => {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const stream = canvas.captureStream(30); // 30 FPS
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "video/webm;codecs=vp9",
      });

      const chunks = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const flippedBlob = new Blob(chunks, { type: "video/webm" });
        URL.revokeObjectURL(video.src);
        resolve(flippedBlob);
      };

      // Start recording
      mediaRecorder.start();
      video.play();

      // Draw flipped frames
      const drawFrame = () => {
        if (video.ended || video.paused) {
          mediaRecorder.stop();
          return;
        }

        // Save the current context state
        ctx.save();

        // Flip horizontally by scaling X by -1 and translating
        ctx.scale(-1, 1);
        ctx.translate(-canvas.width, 0);

        // Draw the flipped video frame
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Restore the context state
        ctx.restore();

        requestAnimationFrame(drawFrame);
      };

      video.addEventListener("play", drawFrame);
    };

    video.onerror = () => {
      reject(new Error("Video loading failed"));
    };
  });
}

// Utility function to escape HTML
function escapeHtml(text) {
  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
}

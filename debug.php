<?php
// Debug page to check system status
require_once 'config.php';

echo "<h1>Dastare System Debug</h1>";

// Check database connection
echo "<h2>Database Connection</h2>";
try {
    $conn = getDBConnection();
    echo "✅ Database connection successful<br>";
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows > 0) {
        echo "✅ Users table exists<br>";
        
        // Check user count
        $userCount = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc();
        echo "👥 Total users: " . $userCount['count'] . "<br>";
    } else {
        echo "❌ Users table does not exist<br>";
    }
    
    // Check if daily_progress has user_id column
    $result = $conn->query("SHOW COLUMNS FROM daily_progress LIKE 'user_id'");
    if ($result->num_rows > 0) {
        echo "✅ daily_progress table has user_id column<br>";
    } else {
        echo "❌ daily_progress table missing user_id column<br>";
    }
    
    // Check if study_sessions has user_id column
    $result = $conn->query("SHOW COLUMNS FROM study_sessions LIKE 'user_id'");
    if ($result->num_rows > 0) {
        echo "✅ study_sessions table has user_id column<br>";
    } else {
        echo "❌ study_sessions table missing user_id column<br>";
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Check session
echo "<h2>Session Status</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "Session ID: " . session_id() . "<br>";
    
    if (isLoggedIn()) {
        echo "✅ User is logged in<br>";
        echo "User ID: " . $_SESSION['user_id'] . "<br>";
        echo "User Name: " . $_SESSION['user_name'] . "<br>";
        echo "User Email: " . $_SESSION['user_email'] . "<br>";
    } else {
        echo "❌ User is not logged in<br>";
    }
} else {
    echo "❌ Session is not active<br>";
}

// Check Google OAuth config
echo "<h2>Google OAuth Configuration</h2>";
if (defined('GOOGLE_CLIENT_ID') && GOOGLE_CLIENT_ID !== 'YOUR_GOOGLE_CLIENT_ID_HERE') {
    echo "✅ Google Client ID is configured<br>";
} else {
    echo "❌ Google Client ID is not configured<br>";
}

if (defined('GOOGLE_CLIENT_SECRET') && GOOGLE_CLIENT_SECRET !== 'YOUR_GOOGLE_CLIENT_SECRET_HERE') {
    echo "✅ Google Client Secret is configured<br>";
} else {
    echo "❌ Google Client Secret is not configured<br>";
}

// Check file permissions
echo "<h2>File System</h2>";
if (is_writable(__DIR__)) {
    echo "✅ Directory is writable<br>";
} else {
    echo "❌ Directory is not writable<br>";
}

if (file_exists('uploads/videos/')) {
    echo "✅ Upload directory exists<br>";
} else {
    echo "❌ Upload directory does not exist<br>";
}

// Check PHP version and extensions
echo "<h2>PHP Environment</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";

$required_extensions = ['mysqli', 'json', 'session'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext extension loaded<br>";
    } else {
        echo "❌ $ext extension not loaded<br>";
    }
}

echo "<h2>Quick Actions</h2>";
echo "<a href='setup_auth.php'>🔧 Setup Authentication</a><br>";
echo "<a href='auth/login.php'>🔐 Login Page</a><br>";
echo "<a href='index.php'>🏠 Home Page</a><br>";

if (isLoggedIn()) {
    echo "<a href='profile.php'>👤 Profile Page</a><br>";
    echo "<a href='auth/logout.php'>🚪 Logout</a><br>";
}
?>

<?php
require_once 'config.php';

// Require authentication
requireAuth();

// Get current user
$currentUser = getCurrentUser();
$userId = getUserId();

// Handle search
$searchTerm = isset($_GET['q']) ? trim($_GET['q']) : '';
$words = [];

if ($searchTerm) {
    $words = fetchAll("SELECT * FROM words WHERE word LIKE ? ORDER BY word LIMIT 20", ["%$searchTerm%"]);
} else {
    // Show initial words if no search term - infinite scroll will load more
    $words = fetchAll("SELECT * FROM words ORDER BY word LIMIT 20");
}

$totalWords = getCount("SELECT COUNT(*) FROM words");
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getLanguageDirection(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('nav_search'); ?> - <?php echo __('app_title'); ?></title>
    <script>
        // Set initial dark mode before page renders
        (function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                if (document.documentElement) {
                    document.documentElement.classList.add('dark-mode');
                }
                // Wait for body to be available
                if (document.body) {
                    document.body.classList.add('dark-mode');
                } else {
                    document.addEventListener('DOMContentLoaded', function() {
                        document.body.classList.add('dark-mode');
                    });
                }
            }
        })();
    </script>

    <!-- PWA Meta Tags -->
    <meta name="description" content="گەڕان لە وشەکانی زمانی ئاماژەی کوردی">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دەستەڕێ">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/192.png">
    <link rel="manifest" href="manifest.json">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/search.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <?php
        // Override subtitle for search page
        $pageSubtitle = __('search_words');
        include 'includes/header.php';
        ?>

        <!-- Navigation -->
        <nav class="nav">
            <div class="nav-container">
                <a href="index.php" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span><?php echo __('nav_home'); ?></span>
                </a>
                <a href="search.php" class="nav-item active">
                    <i class="fas fa-search"></i>
                    <span><?php echo __('nav_search'); ?></span>
                </a>
                <a href="study.php" class="nav-item">
                    <i class="fas fa-graduation-cap"></i>
                    <span><?php echo __('nav_study'); ?></span>
                </a>
                <a href="progress.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span><?php echo __('nav_progress'); ?></span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main">
            <!-- Search Section -->
            <div class="search-section">
                <div class="search-header">
                    <h2><?php echo __('search_words'); ?></h2>
                    <button class="add-word-btn" onclick="openAddWordModal()">
                        <i class="fas fa-plus"></i>
                        <?php echo __('add_new_word'); ?>
                    </button>
                </div>

                <div class="search-container">
                    <form class="search-form" method="GET">
                        <div class="search-input-group">
                            <input
                                type="text"
                                name="q"
                                value="<?php echo htmlspecialchars($searchTerm); ?>"
                                placeholder="<?php echo __('search_placeholder'); ?>"
                                class="search-input"
                                autocomplete="off"
                            >
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>

                    <div class="search-info">
                        <?php if ($searchTerm): ?>
                            <p><?php echo count($words); ?> <?php echo __('search_results'); ?> "<?php echo htmlspecialchars($searchTerm); ?>"</p>
                        <?php else: ?>
                            <p><?php echo __('total_words'); ?>: <?php echo number_format($totalWords); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Words Grid -->
            <div class="words-section">
                <?php if (empty($words)): ?>
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <h3>هیچ وشەیەک نەدۆزرایەوە</h3>
                        <p>تکایە وشەیەکی تر تاقی بکەرەوە</p>
                    </div>
                <?php else: ?>
                    <div class="words-grid">
                        <?php foreach ($words as $word): ?>
                            <div class="word-card" onclick="openWordModal(<?php echo $word['id']; ?>)">
                                <div class="word-content">
                                    <h3 class="word-text"><?php echo htmlspecialchars($word['word']); ?></h3>
                                    <?php if ($word['times_studied'] > 0): ?>
                                        <div class="word-meta">
                                            <span class="study-count">
                                                <i class="fas fa-brain"></i>
                                                <?php echo $word['times_studied']; ?> جار خوێندراوە
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; ٢٠٢٥ ئەمازە - فێربوونی زمانی ئاماژە</p>
        </footer>
    </div>

    <!-- Word Modal -->
    <div id="wordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalWordText"></h3>
                <button class="close-btn" onclick="closeWordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="videoContainer" class="video-container">
                    <video id="wordVideo" controls muted style="display: none;">
                        <source src="" type="video/mp4">
                        وەشانەکەت پشتگیری ڤیدیۆ ناکات.
                    </video>
                    <div id="noVideoMessage" class="no-video-message">
                        <i class="fas fa-video-slash"></i>
                        <p>ڤیدیۆ بۆ ئەم وشەیە نییە</p>
                        <button class="upload-btn" onclick="showVideoUpload()">
                            <i class="fas fa-upload"></i>
                            ڤیدیۆ زیاد بکە
                        </button>
                    </div>
                </div>

                <div id="videoUploadSection" class="video-upload-section" style="display: none;">
                    <h4>ڤیدیۆ زیاد بکە</h4>
                    <form id="videoUploadForm" enctype="multipart/form-data">
                        <input type="hidden" id="uploadWordId" name="word_id">

                        <div class="video-input-options">
                            <div class="video-option-buttons">
                                <button type="button" class="video-option-btn" onclick="showRecordVideo('modal')">
                                    <i class="fas fa-video"></i>
                                    تۆمارکردنی ڤیدیۆ
                                </button>
                                <button type="button" class="video-option-btn" onclick="showSelectVideo('modal')">
                                    <i class="fas fa-folder-open"></i>
                                    هەڵبژاردن لە ئامێر
                                </button>
                            </div>

                            <!-- Video Recording Section -->
                            <div id="modalVideoRecording" class="video-recording-section" style="display: none;">
                                <div class="recording-container">
                                    <video id="modalCameraPreview" class="camera-preview" autoplay muted></video>
                                    <video id="modalRecordedPreview" class="recorded-preview" controls muted style="display: none;"></video>
                                </div>
                                <div class="recording-controls">
                                    <button type="button" id="modalStartRecord" class="record-btn" onclick="startRecording('modal')">
                                        <i class="fas fa-circle"></i>
                                    </button>
                                    <button type="button" id="modalStopRecord" class="record-btn stop" onclick="stopRecording('modal')" style="display: none;">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button type="button" id="modalRetakeRecord" class="record-btn" onclick="retakeRecording('modal')" style="display: none;">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                </div>
                                <div class="recording-status">
                                    <span id="modalRecordingStatus">ئامادەیی بۆ تۆمارکردن</span>
                                </div>
                            </div>

                            <!-- File Selection -->
                            <div id="modalVideoSelection" class="video-selection-section" style="display: none;">
                                <div class="upload-area">
                                    <input type="file" id="videoFile" name="video" accept="video/*" onchange="handleVideoFileSelect(event, 'modal')">
                                    <label for="videoFile" class="upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>ڤیدیۆ هەڵبژێرە یان ڕایکێشە بۆ ئێرە</span>
                                    </label>
                                </div>

                                <!-- Video Cropping Interface -->
                                <div id="modalVideoCropper" class="video-cropper-section" style="display: none;">
                                    <h5>ناوچەی بڕین هەڵبژێرە</h5>
                                    <div class="cropper-container">
                                        <video id="modalCropperVideo" class="cropper-video" controls muted></video>
                                        <div class="crop-overlay">
                                            <div class="crop-selection" id="modalCropSelection">
                                                <div class="resize-handle resize-se"></div>
                                                <div class="resize-handle resize-sw"></div>
                                                <div class="resize-handle resize-ne"></div>
                                                <div class="resize-handle resize-nw"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="cropper-controls">
                                        <button type="button" class="crop-btn" onclick="applyCrop('modal')">
                                            <i class="fas fa-crop"></i>
                                            بڕین
                                        </button>
                                        <button type="button" class="crop-btn secondary" onclick="cancelCrop('modal')">
                                            <i class="fas fa-times"></i>
                                            پاشگەزبوونەوە
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="upload-actions">
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-upload"></i>
                                بارکردن
                            </button>
                            <button type="button" onclick="hideVideoUpload()" class="btn-secondary">
                                پاشگەزبوونەوە
                            </button>
                        </div>
                    </form>
                </div>

                <div class="word-details">
                    <div class="detail-item">
                        <label>وەسف:</label>
                        <p id="wordDescription">-</p>
                    </div>
                    <div class="detail-item">
                        <label>ئاستی سەختی:</label>
                        <p id="wordDifficulty">-</p>
                    </div>
                    <div class="detail-item">
                        <label>جاری خوێندن:</label>
                        <p id="wordStudyCount">-</p>
                    </div>
                    <div class="detail-item">
                        <label>ڕێژەی سەرکەوتن:</label>
                        <p id="wordSuccessRate">-</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="editCurrentWord()">
                    <i class="fas fa-edit"></i>
                    دەستکاری کردن
                </button>
                <button class="btn-danger" onclick="deleteCurrentWordFromModal()">
                    <i class="fas fa-trash"></i>
                    سڕینەوەی وشە
                </button>
                <button class="btn-secondary" onclick="closeWordModal()">
                    <i class="fas fa-times"></i>
                    داخستن
                </button>
            </div>
        </div>
    </div>

    <!-- Add Word Modal -->
    <div id="addWordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>زیادکردنی وشەی نوێ</h3>
                <button class="close-btn" onclick="closeAddWordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addWordForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="newWord">وشە:</label>
                        <input type="text" id="newWord" name="word" required>
                    </div>

                    <div class="form-group">
                        <label for="newDescription">وەسف (ئیختیاری):</label>
                        <textarea id="newDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="newDifficulty">ئاستی سەختی:</label>
                        <select id="newDifficulty" name="difficulty">
                            <option value="easy">ئاسان</option>
                            <option value="medium" selected>مامناوەند</option>
                            <option value="hard">سەخت</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="newVideo">ڤیدیۆ (ئیختیاری):</label>
                        <div class="video-input-options">
                            <div class="video-option-buttons">
                                <button type="button" class="video-option-btn" onclick="showRecordVideo('add')">
                                    <i class="fas fa-video"></i>
                                    تۆمارکردنی ڤیدیۆ
                                </button>
                                <button type="button" class="video-option-btn" onclick="showSelectVideo('add')">
                                    <i class="fas fa-folder-open"></i>
                                    هەڵبژاردن لە ئامێر
                                </button>
                            </div>

                            <!-- Video Recording Section -->
                            <div id="addVideoRecording" class="video-recording-section" style="display: none;">
                                <div class="recording-container">
                                    <video id="addCameraPreview" class="camera-preview" autoplay muted></video>
                                    <video id="addRecordedPreview" class="recorded-preview" controls muted style="display: none;"></video>
                                </div>
                                <div class="recording-controls">
                                    <button type="button" id="addStartRecord" class="record-btn" onclick="startRecording('add')">
                                        <i class="fas fa-circle"></i>
                                    </button>
                                    <button type="button" id="addStopRecord" class="record-btn stop" onclick="stopRecording('add')" style="display: none;">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button type="button" id="addRetakeRecord" class="record-btn" onclick="retakeRecording('add')" style="display: none;">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                </div>
                                <div class="recording-status">
                                    <span id="addRecordingStatus">ئامادەیی بۆ تۆمارکردن</span>
                                </div>
                            </div>

                            <!-- File Selection -->
                            <div id="addVideoSelection" class="video-selection-section" style="display: none;">
                                <input type="file" id="newVideo" name="video" accept="video/*" onchange="handleVideoFileSelect(event, 'add')">

                                <!-- Video Cropping Interface -->
                                <div id="addVideoCropper" class="video-cropper-section" style="display: none;">
                                    <h5>ناوچەی بڕین هەڵبژێرە</h5>
                                    <div class="cropper-container">
                                        <video id="addCropperVideo" class="cropper-video" controls muted></video>
                                        <div class="crop-overlay">
                                            <div class="crop-selection" id="addCropSelection">
                                                <div class="resize-handle resize-se"></div>
                                                <div class="resize-handle resize-sw"></div>
                                                <div class="resize-handle resize-ne"></div>
                                                <div class="resize-handle resize-nw"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="cropper-controls">
                                        <button type="button" class="crop-btn" onclick="applyCrop('add')">
                                            <i class="fas fa-crop"></i>
                                            بڕین
                                        </button>
                                        <button type="button" class="crop-btn secondary" onclick="cancelCrop('add')">
                                            <i class="fas fa-times"></i>
                                            پاشگەزبوونەوە
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-plus"></i>
                            زیادکردن
                        </button>
                        <button type="button" onclick="closeAddWordModal()" class="btn-secondary">
                            پاشگەزبوونەوە
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Word Modal -->
    <div id="editWordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>دەستکاری کردنی وشە</h3>
                <button class="close-btn" onclick="closeEditWordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editWordForm" enctype="multipart/form-data">
                    <input type="hidden" id="editWordId" name="word_id">

                    <div class="form-group">
                        <label for="editWord">وشە:</label>
                        <input type="text" id="editWord" name="word" required>
                    </div>

                    <div class="form-group">
                        <label for="editDescription">وەسف (ئیختیاری):</label>
                        <textarea id="editDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="editDifficulty">ئاستی سەختی:</label>
                        <select id="editDifficulty" name="difficulty">
                            <option value="easy">ئاسان</option>
                            <option value="medium">مامناوەند</option>
                            <option value="hard">سەخت</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>ڤیدیۆی ئێستا:</label>
                        <div id="currentVideoSection">
                            <video id="currentEditVideo" controls muted style="width: 100%; max-width: 300px; display: none;">
                                <source src="" type="video/mp4">
                            </video>
                            <p id="noCurrentVideo" style="color: #6b7280;">ڤیدیۆ نییە</p>
                            <div id="videoActions" style="margin-top: 1rem; display: none;">
                                <button type="button" class="delete-video-btn" onclick="deleteCurrentVideo()">
                                    <i class="fas fa-trash"></i>
                                    سڕینەوەی ڤیدیۆ
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editVideo">ڤیدیۆی نوێ (ئیختیاری):</label>
                        <div class="video-input-options">
                            <div class="video-option-buttons">
                                <button type="button" class="video-option-btn" onclick="showRecordVideo('edit')">
                                    <i class="fas fa-video"></i>
                                    تۆمارکردنی ڤیدیۆ
                                </button>
                                <button type="button" class="video-option-btn" onclick="showSelectVideo('edit')">
                                    <i class="fas fa-folder-open"></i>
                                    هەڵبژاردن لە ئامێر
                                </button>
                            </div>

                            <!-- Video Recording Section -->
                            <div id="editVideoRecording" class="video-recording-section" style="display: none;">
                                <div class="recording-container">
                                    <video id="editCameraPreview" class="camera-preview" autoplay muted></video>
                                    <video id="editRecordedPreview" class="recorded-preview" controls muted style="display: none;"></video>
                                </div>
                                <div class="recording-controls">
                                    <button type="button" id="editStartRecord" class="record-btn" onclick="startRecording('edit')">
                                        <i class="fas fa-circle"></i>
                                    </button>
                                    <button type="button" id="editStopRecord" class="record-btn stop" onclick="stopRecording('edit')" style="display: none;">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button type="button" id="editRetakeRecord" class="record-btn" onclick="retakeRecording('edit')" style="display: none;">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                </div>
                                <div class="recording-status">
                                    <span id="editRecordingStatus">ئامادەیی بۆ تۆمارکردن</span>
                                </div>
                            </div>

                            <!-- File Selection -->
                            <div id="editVideoSelection" class="video-selection-section" style="display: none;">
                                <input type="file" id="editVideo" name="video" accept="video/*" onchange="handleVideoFileSelect(event, 'edit')">

                                <!-- Video Cropping Interface -->
                                <div id="editVideoCropper" class="video-cropper-section" style="display: none;">
                                    <h5>ناوچەی بڕین هەڵبژێرە</h5>
                                    <div class="cropper-container">
                                        <video id="editCropperVideo" class="cropper-video" controls muted></video>
                                        <div class="crop-overlay">
                                            <div class="crop-selection" id="editCropSelection">
                                                <div class="resize-handle resize-se"></div>
                                                <div class="resize-handle resize-sw"></div>
                                                <div class="resize-handle resize-ne"></div>
                                                <div class="resize-handle resize-nw"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="cropper-controls">
                                        <button type="button" class="crop-btn" onclick="applyCrop('edit')">
                                            <i class="fas fa-crop"></i>
                                            بڕین
                                        </button>
                                        <button type="button" class="crop-btn secondary" onclick="cancelCrop('edit')">
                                            <i class="fas fa-times"></i>
                                            پاشگەزبوونەوە
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <small style="color: #6b7280; display: block; margin-top: 0.5rem;">ئەگەر ڤیدیۆیەکی نوێ زیاد بکەیت، ڤیدیۆی کۆن دەسڕێتەوە</small>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save"></i>
                            پاشەکەوتکردن
                        </button>
                        <button type="button" onclick="closeEditWordModal()" class="btn-secondary">
                            پاشگەزبوونەوە
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script src="js/search.js"></script>
    <script src="js/shared.js"></script>
    <script>
        // Initialize dark mode on page load
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.body.classList.add('dark-mode');
                document.documentElement.classList.add('dark-mode');
            }
        });
    </script>
</body>
</html>

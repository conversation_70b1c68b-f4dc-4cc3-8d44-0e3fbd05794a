<?php
// Update daily progress API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }

    // Check authentication
    if (!isLoggedIn()) {
        throw new Exception('پێویستە بچیتە ژوورەوە');
    }

    $userId = getUserId();

    $input = json_decode(file_get_contents('php://input'), true);

    $wordsStudied = isset($input['words_studied']) ? (int)$input['words_studied'] : 0;
    $wordsRemembered = isset($input['words_remembered']) ? (int)$input['words_remembered'] : 0;
    $wordsForgot = isset($input['words_forgot']) ? (int)$input['words_forgot'] : 0;
    $studyTime = isset($input['study_time_minutes']) ? (int)$input['study_time_minutes'] : 0;

    $today = date('Y-m-d');

    // Calculate success rate
    $successRate = $wordsStudied > 0 ? ($wordsRemembered / $wordsStudied) * 100 : 0;

    $conn = getDBConnection();

    // Check if today's progress exists for current user
    $existing = fetchRow("SELECT * FROM daily_progress WHERE user_id = ? AND study_date = ?", [$userId, $today]);

    if ($existing) {
        // Update existing record
        $newWordsStudied = $existing['words_studied'] + $wordsStudied;
        $newWordsRemembered = $existing['words_remembered'] + $wordsRemembered;
        $newWordsForgot = $existing['words_forgot'] + $wordsForgot;
        $newStudyTime = $existing['study_time_minutes'] + $studyTime;
        $newSuccessRate = $newWordsStudied > 0 ? ($newWordsRemembered / $newWordsStudied) * 100 : 0;

        $stmt = $conn->prepare("UPDATE daily_progress SET words_studied = ?, words_remembered = ?, words_forgot = ?, success_rate = ?, study_time_minutes = ? WHERE user_id = ? AND study_date = ?");
        $stmt->bind_param("iiidiis", $newWordsStudied, $newWordsRemembered, $newWordsForgot, $newSuccessRate, $newStudyTime, $userId, $today);
    } else {
        // Insert new record
        $stmt = $conn->prepare("INSERT INTO daily_progress (user_id, study_date, words_studied, words_remembered, words_forgot, success_rate, study_time_minutes) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("isiiidi", $userId, $today, $wordsStudied, $wordsRemembered, $wordsForgot, $successRate, $studyTime);
    }

    if (!$stmt->execute()) {
        throw new Exception('هەڵەیەک ڕوویدا لە نوێکردنەوەی پێشکەوتن');
    }

    $stmt->close();
    $conn->close();

    echo json_encode([
        'success' => true,
        'message' => 'پێشکەوتن بە سەرکەوتوویی نوێکرایەوە'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

<?php
// Delete video API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $wordId = isset($input['word_id']) ? (int)$input['word_id'] : 0;
    
    if ($wordId <= 0) {
        throw new Exception('ناسنامەی وشە نادروستە');
    }
    
    // Get word details before deletion
    $word = fetchRow("SELECT * FROM words WHERE id = ?", [$wordId]);
    if (!$word) {
        throw new Exception('وشە نەدۆزرایەوە');
    }
    
    if (empty($word['video_path'])) {
        throw new Exception('ئەم وشەیە ڤیدیۆی نییە');
    }
    
    $conn = getDBConnection();
    
    // Delete video file from filesystem
    $filePath = UPLOAD_DIR . basename($word['video_path']);
    if (file_exists($filePath)) {
        if (!unlink($filePath)) {
            error_log("Failed to delete video file: " . $filePath);
            // Continue anyway - we'll clear the database reference
        }
    }
    
    // Remove video path from database
    $stmt = $conn->prepare("UPDATE words SET video_path = NULL WHERE id = ?");
    $stmt->bind_param("i", $wordId);
    
    if (!$stmt->execute()) {
        throw new Exception('هەڵەیەک ڕوویدا لە سڕینەوەی ڤیدیۆ لە بنکەی زانیاری');
    }
    
    $stmt->close();
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'ڤیدیۆ بە سەرکەوتوویی سڕایەوە'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

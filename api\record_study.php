<?php
// Record study session API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }

    // Check authentication
    if (!isLoggedIn()) {
        throw new Exception('پێویستە بچیتە ژوورەوە');
    }

    $userId = getUserId();

    $input = json_decode(file_get_contents('php://input'), true);

    $wordId = isset($input['word_id']) ? (int)$input['word_id'] : 0;
    $result = isset($input['result']) ? $input['result'] : '';
    $responseTime = isset($input['response_time']) ? (int)$input['response_time'] : null;

    if ($wordId <= 0) {
        throw new Exception('ناسنامەی وشە نادروستە');
    }

    if (!in_array($result, ['remembered', 'forgot'])) {
        throw new Exception('ئەنجامی خوێندن نادروستە');
    }

    $conn = getDBConnection();

    // Record study session
    $stmt = $conn->prepare("INSERT INTO study_sessions (user_id, word_id, result, response_time_seconds, session_date) VALUES (?, ?, ?, ?, CURDATE())");
    $stmt->bind_param("iisi", $userId, $wordId, $result, $responseTime);

    if (!$stmt->execute()) {
        throw new Exception('هەڵەیەک ڕوویدا لە تۆمارکردنی خوێندن');
    }
    $stmt->close();

    // Update word statistics
    if ($result === 'remembered') {
        $updateQuery = "UPDATE words SET times_studied = times_studied + 1, times_remembered = times_remembered + 1 WHERE id = ?";
    } else {
        $updateQuery = "UPDATE words SET times_studied = times_studied + 1, times_forgot = times_forgot + 1 WHERE id = ?";
    }

    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("i", $wordId);
    $stmt->execute();
    $stmt->close();

    // Update success rate
    $stmt = $conn->prepare("UPDATE words SET success_rate = CASE WHEN times_studied > 0 THEN (times_remembered / times_studied) * 100 ELSE 0 END WHERE id = ?");
    $stmt->bind_param("i", $wordId);
    $stmt->execute();
    $stmt->close();

    $conn->close();

    echo json_encode([
        'success' => true,
        'message' => 'خوێندن بە سەرکەوتوویی تۆمارکرا'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

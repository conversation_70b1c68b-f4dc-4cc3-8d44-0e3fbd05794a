<?php
require_once '../config.php';

try {
    // Check authentication
    if (!isLoggedIn()) {
        http_response_code(401);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => false, 'message' => 'پێویستە بچیتە ژوورەوە']);
        exit;
    }

    $userId = getUserId();
    $format = $_GET['format'] ?? 'json';
    
    // Get user data
    $user = getCurrentUser();
    
    // Get study sessions
    $studySessions = fetchAll("
        SELECT ss.*, w.word, w.category 
        FROM study_sessions ss 
        JOIN words w ON ss.word_id = w.id 
        WHERE ss.user_id = ? 
        ORDER BY ss.created_at DESC
    ", [$userId]);
    
    // Get daily progress
    $dailyProgress = fetchAll("
        SELECT * FROM daily_progress 
        WHERE user_id = ? 
        ORDER BY study_date DESC
    ", [$userId]);
    
    // Calculate statistics
    $totalWords = getCount("SELECT COUNT(DISTINCT word_id) FROM study_sessions WHERE user_id = ?", [$userId]);
    $totalSessions = getCount("SELECT COUNT(*) FROM study_sessions WHERE user_id = ?", [$userId]);
    $rememberedCount = getCount("SELECT COUNT(*) FROM study_sessions WHERE user_id = ? AND result = 'remembered'", [$userId]);
    $forgotCount = getCount("SELECT COUNT(*) FROM study_sessions WHERE user_id = ? AND result = 'forgot'", [$userId]);
    $averageResponseTime = fetchRow("SELECT AVG(response_time_seconds) as avg_time FROM study_sessions WHERE user_id = ? AND response_time_seconds IS NOT NULL", [$userId]);
    
    $exportData = [
        'export_info' => [
            'exported_at' => date('Y-m-d H:i:s'),
            'user_id' => $userId,
            'user_name' => $user['name'],
            'user_email' => $user['email'],
            'member_since' => $user['created_at']
        ],
        'statistics' => [
            'total_words_studied' => $totalWords,
            'total_study_sessions' => $totalSessions,
            'words_remembered' => $rememberedCount,
            'words_forgot' => $forgotCount,
            'success_rate' => $totalSessions > 0 ? round(($rememberedCount / $totalSessions) * 100, 2) : 0,
            'average_response_time_seconds' => round($averageResponseTime['avg_time'] ?? 0, 2)
        ],
        'study_sessions' => $studySessions,
        'daily_progress' => $dailyProgress
    ];
    
    if ($format === 'csv') {
        // Export as CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="dastare_data_' . date('Y-m-d') . '.csv"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Export study sessions
        fputcsv($output, ['Study Sessions']);
        fputcsv($output, ['Date', 'Word', 'Category', 'Result', 'Response Time (seconds)']);
        
        foreach ($studySessions as $session) {
            fputcsv($output, [
                $session['session_date'],
                $session['word'],
                $session['category'],
                $session['result'],
                $session['response_time_seconds'] ?? ''
            ]);
        }
        
        // Add empty row
        fputcsv($output, []);
        
        // Export daily progress
        fputcsv($output, ['Daily Progress']);
        fputcsv($output, ['Date', 'Words Studied', 'Words Remembered', 'Words Forgot', 'Success Rate %', 'Study Time (minutes)']);
        
        foreach ($dailyProgress as $progress) {
            fputcsv($output, [
                $progress['study_date'],
                $progress['words_studied'],
                $progress['words_remembered'],
                $progress['words_forgot'],
                $progress['success_rate'],
                $progress['study_time_minutes']
            ]);
        }
        
        fclose($output);
        exit;
        
    } else {
        // Export as JSON
        header('Content-Type: application/json; charset=utf-8');
        header('Content-Disposition: attachment; filename="dastare_data_' . date('Y-m-d') . '.json"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        echo json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

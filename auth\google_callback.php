<?php
require_once '../config.php';
require_once '../auth_config.php';

try {
    // Check for errors
    if (isset($_GET['error'])) {
        throw new Exception('OAuth error: ' . $_GET['error']);
    }
    
    // Verify state parameter
    if (!isset($_GET['state']) || $_GET['state'] !== $_SESSION['oauth_state']) {
        throw new Exception('Invalid state parameter');
    }
    
    // Get authorization code
    if (!isset($_GET['code'])) {
        throw new Exception('No authorization code received');
    }
    
    $code = $_GET['code'];
    
    // Exchange code for access token
    $tokenData = getGoogleAccessToken($code);
    
    if (!isset($tokenData['access_token'])) {
        throw new Exception('Failed to get access token');
    }
    
    // Get user information
    $googleUser = getGoogleUserInfo($tokenData['access_token']);
    
    if (!$googleUser || !isset($googleUser['id'])) {
        throw new Exception('Failed to get user information');
    }
    
    // Create or update user in database
    $user = createOrUpdateUser($googleUser);
    
    // Login user
    loginUser($user);
    
    // Clear OAuth state
    unset($_SESSION['oauth_state']);
    
    // Redirect to dashboard
    header('Location: ../index.php?welcome=1');
    exit;
    
} catch (Exception $e) {
    error_log('OAuth Error: ' . $e->getMessage());
    
    // Redirect to login with error
    header('Location: login.php?error=' . urlencode('خەتایەک ڕوویدا لە چوونەژوورەوە. تکایە دووبارە هەوڵ بدەوە.'));
    exit;
}
?>

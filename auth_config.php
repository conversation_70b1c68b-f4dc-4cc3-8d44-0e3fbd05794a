<?php
// Google OAuth Configuration for Dastare Sign Language Learning App

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Google OAuth 2.0 Configuration
// You need to create a Google Cloud Project and enable Google+ API
// Then create OAuth 2.0 credentials (Web application)
// Add your domain to authorized origins and redirect URIs

define('GOOGLE_CLIENT_ID', '***********-e1is0u92l611maavk7mino8kll4fg28c.apps.googleusercontent.com');
define('GOOGLE_CLIENT_SECRET', '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');
define('GOOGLE_REDIRECT_URI', 'http://localhost/dastare/auth/google_callback.php');

// OAuth URLs
define('GOOGLE_AUTH_URL', 'https://accounts.google.com/o/oauth2/v2/auth');
define('GOOGLE_TOKEN_URL', 'https://oauth2.googleapis.com/token');
define('GOOGLE_USER_INFO_URL', 'https://www.googleapis.com/oauth2/v2/userinfo');

// OAuth Scopes
define('GOOGLE_SCOPES', 'openid email profile');

// Session configuration
define('SESSION_TIMEOUT', 3600 * 24 * 30); // 30 days

/**
 * Generate Google OAuth login URL
 */
function getGoogleAuthUrl() {
    $state = bin2hex(random_bytes(16));
    $_SESSION['oauth_state'] = $state;

    $params = [
        'client_id' => GOOGLE_CLIENT_ID,
        'redirect_uri' => GOOGLE_REDIRECT_URI,
        'scope' => GOOGLE_SCOPES,
        'response_type' => 'code',
        'state' => $state,
        'access_type' => 'offline',
        'prompt' => 'consent'
    ];

    return GOOGLE_AUTH_URL . '?' . http_build_query($params);
}

/**
 * Exchange authorization code for access token
 */
function getGoogleAccessToken($code) {
    $data = [
        'client_id' => GOOGLE_CLIENT_ID,
        'client_secret' => GOOGLE_CLIENT_SECRET,
        'redirect_uri' => GOOGLE_REDIRECT_URI,
        'grant_type' => 'authorization_code',
        'code' => $code
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    $context = stream_context_create($options);
    $response = file_get_contents(GOOGLE_TOKEN_URL, false, $context);

    if ($response === false) {
        throw new Exception('Failed to get access token');
    }

    return json_decode($response, true);
}

/**
 * Get user information from Google
 */
function getGoogleUserInfo($accessToken) {
    $options = [
        'http' => [
            'header' => "Authorization: Bearer " . $accessToken . "\r\n",
            'method' => 'GET'
        ]
    ];

    $context = stream_context_create($options);
    $response = file_get_contents(GOOGLE_USER_INFO_URL, false, $context);

    if ($response === false) {
        throw new Exception('Failed to get user info');
    }

    return json_decode($response, true);
}

/**
 * Create or update user in database
 */
function createOrUpdateUser($googleUser) {
    require_once 'config.php';

    $conn = getDBConnection();

    // Check if user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE google_id = ?");
    $stmt->bind_param("s", $googleUser['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if ($user) {
        // Update existing user
        $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, picture = ?, last_login = NOW() WHERE google_id = ?");
        $stmt->bind_param("ssss", $googleUser['name'], $googleUser['email'], $googleUser['picture'], $googleUser['id']);
        $stmt->execute();
        $stmt->close();

        $user['name'] = $googleUser['name'];
        $user['email'] = $googleUser['email'];
        $user['picture'] = $googleUser['picture'];
    } else {
        // Create new user
        $stmt = $conn->prepare("INSERT INTO users (google_id, email, name, picture, last_login) VALUES (?, ?, ?, ?, NOW())");
        $stmt->bind_param("ssss", $googleUser['id'], $googleUser['email'], $googleUser['name'], $googleUser['picture']);
        $stmt->execute();

        $userId = $conn->insert_id;
        $stmt->close();

        $user = [
            'id' => $userId,
            'google_id' => $googleUser['id'],
            'email' => $googleUser['email'],
            'name' => $googleUser['name'],
            'picture' => $googleUser['picture']
        ];
    }

    $conn->close();
    return $user;
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Get current user information
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    require_once 'config.php';

    $user = fetchRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);
    return $user;
}

/**
 * Require authentication - redirect to login if not logged in
 */
function requireAuth() {
    if (!isLoggedIn()) {
        // Check if we're already in the auth directory
        $loginPath = (strpos($_SERVER['REQUEST_URI'], '/auth/') !== false) ? 'login.php' : 'auth/login.php';
        header('Location: ' . $loginPath);
        exit;
    }
}

/**
 * Login user
 */
function loginUser($user) {
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_picture'] = $user['picture'];
    $_SESSION['login_time'] = time();
}

/**
 * Logout user
 */
function logoutUser() {
    session_unset();
    session_destroy();
    session_start();
}

/**
 * Get user ID for database queries
 */
function getUserId() {
    if (!isLoggedIn()) {
        throw new Exception('User not logged in');
    }
    return $_SESSION['user_id'];
}
?>

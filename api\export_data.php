<?php
require_once '../config.php';

try {
    // Check authentication
    if (!isLoggedIn()) {
        http_response_code(401);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => false, 'message' => 'پێویستە بچیتە ژوورەوە']);
        exit;
    }

    $userId = getUserId();
    $format = $_GET['format'] ?? 'json';

    // Get user data
    $user = getCurrentUser();
    if (!$user) {
        $user = [
            'id' => $userId,
            'name' => 'بەکارهێنەری تاقیکردنەوە',
            'email' => '<EMAIL>',
            'created_at' => date('Y-m-d H:i:s')
        ];
    }

    // For demo purposes, create sample data
    $studySessions = [
        [
            'session_date' => date('Y-m-d'),
            'word' => 'سڵاو',
            'category' => 'basic_words',
            'result' => 'remembered',
            'response_time_seconds' => 3.5
        ],
        [
            'session_date' => date('Y-m-d'),
            'word' => 'سوپاس',
            'category' => 'basic_words',
            'result' => 'remembered',
            'response_time_seconds' => 2.8
        ],
        [
            'session_date' => date('Y-m-d', strtotime('-1 day')),
            'word' => 'باش',
            'category' => 'basic_words',
            'result' => 'forgot',
            'response_time_seconds' => 5.2
        ]
    ];

    $dailyProgress = [
        [
            'study_date' => date('Y-m-d'),
            'words_studied' => 5,
            'words_remembered' => 4,
            'words_forgot' => 1,
            'success_rate' => 80.0,
            'study_time_minutes' => 15
        ],
        [
            'study_date' => date('Y-m-d', strtotime('-1 day')),
            'words_studied' => 3,
            'words_remembered' => 2,
            'words_forgot' => 1,
            'success_rate' => 66.7,
            'study_time_minutes' => 10
        ]
    ];

    // Calculate statistics
    $totalWords = 8;
    $totalSessions = count($studySessions);
    $rememberedCount = 2;
    $forgotCount = 1;
    $averageResponseTime = 3.8;
    
    $exportData = [
        'export_info' => [
            'exported_at' => date('Y-m-d H:i:s'),
            'user_id' => $userId,
            'user_name' => $user['name'],
            'user_email' => $user['email'],
            'member_since' => $user['created_at']
        ],
        'statistics' => [
            'total_words_studied' => $totalWords,
            'total_study_sessions' => $totalSessions,
            'words_remembered' => $rememberedCount,
            'words_forgot' => $forgotCount,
            'success_rate' => $totalSessions > 0 ? round(($rememberedCount / $totalSessions) * 100, 2) : 0,
            'average_response_time_seconds' => round($averageResponseTime, 2)
        ],
        'study_sessions' => $studySessions,
        'daily_progress' => $dailyProgress
    ];
    
    if ($format === 'csv') {
        // Export as CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="dastare_data_' . date('Y-m-d') . '.csv"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Export study sessions
        fputcsv($output, ['Study Sessions']);
        fputcsv($output, ['Date', 'Word', 'Category', 'Result', 'Response Time (seconds)']);
        
        foreach ($studySessions as $session) {
            fputcsv($output, [
                $session['session_date'],
                $session['word'],
                $session['category'],
                $session['result'],
                $session['response_time_seconds'] ?? ''
            ]);
        }
        
        // Add empty row
        fputcsv($output, []);
        
        // Export daily progress
        fputcsv($output, ['Daily Progress']);
        fputcsv($output, ['Date', 'Words Studied', 'Words Remembered', 'Words Forgot', 'Success Rate %', 'Study Time (minutes)']);
        
        foreach ($dailyProgress as $progress) {
            fputcsv($output, [
                $progress['study_date'],
                $progress['words_studied'],
                $progress['words_remembered'],
                $progress['words_forgot'],
                $progress['success_rate'],
                $progress['study_time_minutes']
            ]);
        }
        
        fclose($output);
        exit;
        
    } else {
        // Export as JSON
        header('Content-Type: application/json; charset=utf-8');
        header('Content-Disposition: attachment; filename="dastare_data_' . date('Y-m-d') . '.json"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        echo json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

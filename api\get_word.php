<?php
// Get single word API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if ($id <= 0) {
        throw new Exception('ناسنامەی وشە نادروستە');
    }
    
    $word = fetchRow("SELECT * FROM words WHERE id = ?", [$id]);
    
    if (!$word) {
        throw new Exception('وشە نەدۆزرایەوە');
    }
    
    echo json_encode([
        'success' => true,
        'word' => $word
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

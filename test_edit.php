<?php
// Test edit functionality
require_once 'config.php';

echo "<h2>Edit Functionality Test</h2>";

try {
    // Get a sample word to test with
    $word = fetchRow("SELECT * FROM words LIMIT 1");

    if ($word) {
        echo "<h3>Sample Word for Testing</h3>";
        echo "<p><strong>ID:</strong> {$word['id']}</p>";
        echo "<p><strong>Word:</strong> {$word['word']}</p>";
        echo "<p><strong>Description:</strong> " . ($word['description'] ?: 'None') . "</p>";
        echo "<p><strong>Difficulty:</strong> {$word['difficulty_level']}</p>";
        echo "<p><strong>Video:</strong> " . ($word['video_path'] ?: 'None') . "</p>";

        echo "<h3>Test Edit API</h3>";
        echo "<form method='POST' style='max-width: 500px;'>";
        echo "<input type='hidden' name='test_edit' value='1'>";
        echo "<input type='hidden' name='word_id' value='{$word['id']}'>";

        echo "<div style='margin-bottom: 1rem;'>";
        echo "<label>Word:</label><br>";
        echo "<input type='text' name='word' value='{$word['word']}' style='width: 100%; padding: 0.5rem;'>";
        echo "</div>";

        echo "<div style='margin-bottom: 1rem;'>";
        echo "<label>Description:</label><br>";
        echo "<textarea name='description' style='width: 100%; padding: 0.5rem; height: 80px;'>{$word['description']}</textarea>";
        echo "</div>";

        echo "<div style='margin-bottom: 1rem;'>";
        echo "<label>Difficulty:</label><br>";
        echo "<select name='difficulty' style='width: 100%; padding: 0.5rem;'>";
        echo "<option value='easy'" . ($word['difficulty_level'] == 'easy' ? ' selected' : '') . ">Easy</option>";
        echo "<option value='medium'" . ($word['difficulty_level'] == 'medium' ? ' selected' : '') . ">Medium</option>";
        echo "<option value='hard'" . ($word['difficulty_level'] == 'hard' ? ' selected' : '') . ">Hard</option>";
        echo "</select>";
        echo "</div>";

        echo "<button type='submit' style='background: #667eea; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 8px; cursor: pointer;'>Test Edit</button>";
        echo "</form>";

        // Handle form submission
        if (isset($_POST['test_edit'])) {
            echo "<h3>Edit Test Result</h3>";

            $testData = [
                'word_id' => $_POST['word_id'],
                'word' => $_POST['word'],
                'description' => $_POST['description'],
                'difficulty' => $_POST['difficulty']
            ];

            // Simulate API call
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost/dastare/api/edit_word.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
            echo "<p><strong>Response:</strong></p>";
            echo "<pre style='background: #f0f0f0; padding: 1rem; border-radius: 4px; overflow-x: auto;'>";
            echo htmlspecialchars($response);
            echo "</pre>";

            if ($httpCode == 200) {
                $result = json_decode($response, true);
                if ($result && $result['success']) {
                    echo "<p style='color: green;'>✓ Edit test successful!</p>";

                    // Reload word to show changes
                    $updatedWord = fetchRow("SELECT * FROM words WHERE id = ?", [$_POST['word_id']]);
                    echo "<h4>Updated Word:</h4>";
                    echo "<p><strong>Word:</strong> {$updatedWord['word']}</p>";
                    echo "<p><strong>Description:</strong> " . ($updatedWord['description'] ?: 'None') . "</p>";
                    echo "<p><strong>Difficulty:</strong> {$updatedWord['difficulty_level']}</p>";
                } else {
                    echo "<p style='color: red;'>✗ Edit test failed: " . ($result['message'] ?? 'Unknown error') . "</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ HTTP request failed</p>";
            }
        }

    } else {
        echo "<p style='color: red;'>No words found in database</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>JavaScript Test</h3>";
echo "<p>Open browser console and try:</p>";
echo "<code>editWord(1)</code>";
echo "<p>This should open the edit modal for word ID 1.</p>";

echo "<hr>";
echo "<p><a href='search.php'>← Back to Search Page</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; }
code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
pre { white-space: pre-wrap; }
</style>

/* Modern Sign Language Learning App Styles */
/* Kurdish Sorani RTL Support */

@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap");

:root {
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-card: #ffffff;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Noto Sans Arabic", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: var(--text-primary);
  direction: rtl;
  text-align: right;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.header {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
  text-align: center;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

/* User Info Styles */
.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.8);
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  flex: 1;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-greeting {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.user-email {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Language Selector */
.language-selector {
  position: relative;
}

.language-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: none;
  border-radius: var(--border-radius);
  background: rgba(103, 126, 234, 0.1);
  color: var(--primary-color);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9rem;
}

.language-btn:hover {
  background: var(--primary-color);
  color: white;
}

.language-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  min-width: 150px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.language-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.lang-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--text-primary);
  transition: var(--transition);
  border-bottom: 1px solid var(--border-color);
}

.lang-option:last-child {
  border-bottom: none;
}

.lang-option:hover {
  background: var(--bg-secondary);
}

.lang-option.active {
  background: var(--primary-color);
  color: white;
}

.lang-flag {
  font-size: 1.2rem;
}

.settings-btn,
.notifications-btn {
  position: relative;
  padding: 0.75rem;
  border: none;
  border-radius: 50%;
  background: rgba(103, 126, 234, 0.1);
  color: var(--primary-color);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
}

.settings-btn:hover,
.notifications-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--error-color);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* Welcome Banner Styles */
.welcome-banner {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 2rem;
  margin-bottom: 2rem;
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.welcome-content h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.welcome-content p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.welcome-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.logo {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.logo i {
  font-size: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-image {
  height: 3rem;
  width: auto;
  object-fit: contain;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: var(--transition);
}

.logo-image:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
}

.logo-text {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 400;
}

/* Navigation Styles */
.nav {
  margin-bottom: 2rem;
}

.nav-container {
  display: flex;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 0.5rem;
  box-shadow: var(--shadow-md);
  gap: 0.5rem;
  overflow-x: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border-radius: calc(var(--border-radius) - 4px);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition);
  white-space: nowrap;
  min-width: fit-content;
}

.nav-item:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.nav-item.active {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-item i {
  font-size: 1.2rem;
}

/* Main Content */
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.stat-content p {
  color: var(--text-secondary);
  font-weight: 500;
}

/* Quick Actions */
.quick-actions h2,
.recent-progress h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1.5rem;
  text-align: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-decoration: none;
  color: var(--text-primary);
  transition: var(--transition);
  box-shadow: var(--shadow-md);
  text-align: center;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.action-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
  color: var(--text-primary);
}

.action-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
}

.action-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.action-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Recent Progress */
.progress-list {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
}

.progress-item:last-child {
  border-bottom: none;
}

.progress-item:hover {
  background: var(--bg-secondary);
}

.progress-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-stats {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.stat i {
  color: var(--primary-color);
}

/* Settings and Notifications Panels */
.user-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

.settings-panel,
.notifications-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100%;
  background: white;
  z-index: 999;
  transition: right 0.3s ease;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.settings-panel.open,
.notifications-panel.open {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-color);
  color: white;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.panel-content {
  padding: 1.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item label {
  font-weight: 500;
  color: var(--text-primary);
}

.setting-item input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: var(--primary-color);
}

.setting-item select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: white;
  color: var(--text-primary);
}

.notification-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background: var(--bg-secondary);
  margin: 0 -1.5rem;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
}

.notification-item.unread {
  background: rgba(103, 126, 234, 0.05);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  color: white;
  flex-shrink: 0;
}

.notification-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.notification-content p {
  margin: 0 0 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.notification-time {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Bottom Action Bar */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding: 1rem;
}

/* Add padding to body to prevent content being hidden behind bottom bar */
body {
  padding-bottom: 90px !important;
}

/* Ensure container has proper spacing */
.container {
  padding-bottom: 2rem !important;
  min-height: calc(100vh - 90px);
}

/* Dark mode for bottom action bar */
body.dark-mode .bottom-action-bar,
html.dark-mode .bottom-action-bar {
  background: var(--bg-card) !important;
  border-top-color: var(--border-color) !important;
}

body.dark-mode .action-btn,
html.dark-mode .action-btn {
  color: var(--text-secondary) !important;
}

body.dark-mode .action-btn:hover,
html.dark-mode .action-btn:hover {
  background: var(--bg-secondary) !important;
  color: var(--primary-color) !important;
}

.action-bar-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  border-radius: var(--border-radius);
  min-width: 60px;
}

.action-btn:hover {
  color: var(--primary-color);
  background: rgba(103, 126, 234, 0.1);
}

.action-btn.logout-btn {
  color: var(--error-color);
}

.action-btn.logout-btn:hover {
  color: white;
  background: var(--error-color);
}

.action-btn i {
  font-size: 1.2rem;
}

.action-btn span {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  color: var(--text-primary);
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 1000;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  border-left: 4px solid var(--primary-color);
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast.toast-success {
  border-left-color: var(--success-color);
}

.toast.toast-error {
  border-left-color: var(--error-color);
}

.toast i {
  color: var(--primary-color);
}

.toast.toast-success i {
  color: var(--success-color);
}

.toast.toast-error i {
  color: var(--error-color);
}

/* Footer */
.footer {
  text-align: center;
  padding: 2rem 2rem 6rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-top: auto;
}

.footer-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.8rem;
  transition: var(--transition);
}

.footer-links a:hover {
  color: rgba(255, 255, 255, 0.9);
}

/* Dark Mode Styles */
body.dark-mode,
html.dark-mode body,
html.dark-mode,
.dark-mode {
  --bg-primary: #1a1a2e !important;
  --bg-secondary: #16213e !important;
  --bg-card: #0f3460 !important;
  --text-primary: #ffffff !important;
  --text-secondary: #b8c5d6 !important;
  --border-color: #2d3748 !important;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
  color: var(--text-primary) !important;
}

/* Ensure all containers use dark mode variables */
.dark-mode .container,
body.dark-mode .container,
html.dark-mode .container {
  background: transparent !important;
  color: var(--text-primary) !important;
}

/* Fix all text elements in dark mode */
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode h6,
.dark-mode p,
.dark-mode span,
.dark-mode div,
.dark-mode label,
body.dark-mode h1,
body.dark-mode h2,
body.dark-mode h3,
body.dark-mode h4,
body.dark-mode h5,
body.dark-mode h6,
body.dark-mode p,
body.dark-mode span,
body.dark-mode div,
body.dark-mode label {
  color: var(--text-primary) !important;
}

/* Fix input elements in dark mode */
.dark-mode input,
.dark-mode textarea,
.dark-mode select,
body.dark-mode input,
body.dark-mode textarea,
body.dark-mode select {
  background: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

body.dark-mode .header {
  background: linear-gradient(
    135deg,
    rgba(15, 52, 96, 0.95) 0%,
    rgba(22, 33, 62, 0.9) 100%
  );
}

body.dark-mode .user-info {
  background: rgba(15, 52, 96, 0.8);
}

body.dark-mode .nav-container {
  background: var(--bg-card);
}

body.dark-mode .nav-item {
  color: var(--text-secondary);
}

body.dark-mode .nav-item:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

body.dark-mode .stat-card,
body.dark-mode .action-card,
body.dark-mode .progress-list {
  background: linear-gradient(
    135deg,
    rgba(15, 52, 96, 0.95) 0%,
    rgba(22, 33, 62, 0.9) 100%
  );
}

body.dark-mode .settings-panel,
body.dark-mode .notifications-panel {
  background: var(--bg-card);
  color: var(--text-primary);
}

body.dark-mode .setting-item {
  border-bottom-color: var(--border-color);
}

body.dark-mode .notification-item {
  border-bottom-color: var(--border-color);
}

body.dark-mode .notification-item:hover {
  background: var(--bg-secondary);
}

body.dark-mode .bottom-action-bar {
  background: var(--bg-card);
  border-top-color: var(--border-color);
}

body.dark-mode .toast {
  background: var(--bg-card);
  color: var(--text-primary);
}

body.dark-mode .welcome-banner {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
}

body.dark-mode .language-menu {
  background: var(--bg-card);
  border-color: var(--border-color);
}

body.dark-mode .lang-option {
  color: var(--text-primary);
  border-bottom-color: var(--border-color);
}

body.dark-mode .lang-option:hover {
  background: var(--bg-secondary);
}

body.dark-mode .lang-option.active {
  background: var(--primary-color);
  color: white;
}

/* Language-specific styles */
html[lang="en"] {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

html[lang="ar"] {
  font-family: "Amiri", "Times New Roman", serif;
}

html[lang="ckb"] {
  font-family: "Amiri", "Times New Roman", serif;
}

/* RTL/LTR specific adjustments */
html[dir="ltr"] .header-actions {
  flex-direction: row-reverse;
}

html[dir="ltr"] .language-menu {
  left: 0;
  right: auto;
}

html[dir="ltr"] .nav-container {
  flex-direction: row-reverse;
}

html[dir="ltr"] .action-bar-content {
  flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
    max-width: 100%;
    overflow-x: hidden;
  }

  .header {
    padding: 1.5rem;
    margin: 0 -1rem 2rem;
    border-radius: 0;
    flex-direction: column;
    gap: 1rem;
  }

  .user-info {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .user-details {
    display: none;
  }

  .header-actions {
    gap: 0.25rem;
  }

  .settings-btn,
  .notifications-btn {
    width: 40px;
    height: 40px;
    padding: 0.5rem;
  }

  .settings-panel,
  .notifications-panel {
    width: 100%;
    right: -100%;
  }

  .bottom-action-bar {
    padding: 0.75rem;
  }

  .action-btn {
    padding: 0.5rem;
    min-width: 50px;
  }

  .action-btn span {
    font-size: 0.7rem;
  }

  .toast {
    right: 10px;
    left: 10px;
    transform: translateY(-100%);
  }

  .toast.show {
    transform: translateY(0);
  }

  .nav {
    margin: 0 -1rem 2rem;
  }

  .logo {
    font-size: 2rem;
  }

  .logo i {
    font-size: 2.5rem;
  }

  .logo-image {
    height: 2.5rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .nav-container {
    padding: 0.25rem;
    border-radius: 0;
  }

  .nav-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .action-card {
    padding: 1.5rem;
  }

  .progress-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .progress-stats {
    flex-wrap: wrap;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .progress-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Global Loading and Notification Styles */
.global-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loader-content {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-xl);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9998;
  max-width: 400px;
}

.notification {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideIn 0.3s ease-out;
  border-left: 4px solid var(--primary-color);
}

.notification.notification-success {
  border-left-color: var(--success-color);
}

.notification.notification-error {
  border-left-color: var(--error-color);
}

.notification.notification-warning {
  border-left-color: var(--warning-color);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.notification-content i {
  font-size: 1.2rem;
}

.notification-success .notification-content i {
  color: var(--success-color);
}

.notification-error .notification-content i {
  color: var(--error-color);
}

.notification-warning .notification-content i {
  color: var(--warning-color);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: var(--transition);
}

.notification-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.notification.fade-out {
  animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Fade in animation for cards */
.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tooltip styles */
.tooltip {
  position: absolute;
  background: var(--text-primary);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  white-space: nowrap;
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  animation: tooltipFadeIn 0.2s ease-out forwards;
}

.tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--text-primary);
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* PWA Install Banner Styles */
.pwa-banner {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-xl);
  z-index: 9997;
  animation: slideUp 0.3s ease-out;
  max-width: 500px;
  margin: 0 auto;
}

.pwa-banner-content {
  display: flex;
  align-items: center;
  padding: 1rem;
  gap: 1rem;
}

.pwa-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  flex-shrink: 0;
}

.pwa-text {
  flex: 1;
}

.pwa-text h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.pwa-text p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.pwa-install-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
}

.pwa-install-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.pwa-dismiss-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: var(--transition);
  margin-right: 0.5rem;
}

.pwa-dismiss-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive PWA Banner */
@media (max-width: 768px) {
  .pwa-banner {
    left: 10px;
    right: 10px;
    bottom: 10px;
  }

  .pwa-banner-content {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .pwa-icon {
    width: 40px;
    height: 40px;
  }

  .pwa-text h4 {
    font-size: 0.9rem;
  }

  .pwa-text p {
    font-size: 0.8rem;
  }

  .pwa-install-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* Force Muted Video Styles - Hide Volume Controls */
video.force-muted::-webkit-media-controls-volume-slider,
video.force-muted::-webkit-media-controls-mute-button {
  display: none !important;
}

video.force-muted::-moz-media-controls-volume-slider,
video.force-muted::-moz-media-controls-mute-button {
  display: none !important;
}

/* Additional protection - hide all volume related controls */
video.force-muted::-webkit-media-controls-volume-control-container {
  display: none !important;
}

video.force-muted::-webkit-media-controls-volume-control-hover-background {
  display: none !important;
}

/* Prevent user selection of video to avoid right-click menu */
video.force-muted {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: auto;
}

/* Ensure video controls are still functional except for volume */
video.force-muted::-webkit-media-controls-panel {
  display: flex !important;
}

video.force-muted::-webkit-media-controls-play-button,
video.force-muted::-webkit-media-controls-timeline,
video.force-muted::-webkit-media-controls-current-time-display,
video.force-muted::-webkit-media-controls-time-remaining-display,
video.force-muted::-webkit-media-controls-fullscreen-button {
  display: block !important;
}

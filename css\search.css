/* Search Page Specific Styles */

.search-section {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.search-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.add-word-btn {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.add-word-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-form {
  width: 100%;
}

.search-input-group {
  display: flex;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 2px solid transparent;
  transition: var(--transition);
}

.search-input-group:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  font-size: 1.1rem;
  color: var(--text-primary);
  outline: none;
  font-family: inherit;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  font-size: 1.1rem;
}

.search-btn:hover {
  background: linear-gradient(
    135deg,
    var(--primary-dark),
    var(--secondary-color)
  );
}

.search-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Words Section */
.words-section {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-md);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  backdrop-filter: blur(10px);
}

.words-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Mobile responsive: 2 words per row */
@media (max-width: 768px) {
  .words-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .word-card {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
  }

  .word-text {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    text-align: center;
  }

  .word-meta {
    align-items: center;
    gap: 0.25rem;
  }

  .has-video,
  .no-video,
  .study-count {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
    justify-content: center;
  }

  /* Word actions removed - no longer needed on mobile */

  /* Mobile search section adjustments */
  .search-section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .search-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .search-header h2 {
    font-size: 1.5rem;
    text-align: center;
  }

  .add-word-btn {
    align-self: center;
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }

  .search-input {
    font-size: 1rem;
    padding: 0.875rem 1.25rem;
  }

  .search-btn {
    padding: 0.875rem 1.25rem;
    font-size: 1rem;
  }

  /* Mobile words section adjustments */
  .words-section {
    padding: 1.5rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .words-grid {
    gap: 0.75rem;
  }

  .word-card {
    padding: 0.75rem;
  }

  .word-text {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .has-video,
  .no-video,
  .study-count {
    font-size: 0.7rem;
    padding: 0.15rem 0.3rem;
  }

  /* Action buttons removed - no longer needed */

  .search-section,
  .words-section {
    padding: 1rem;
  }

  .search-input,
  .search-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
}

.word-card {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.word-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.word-content {
  flex: 1;
}

.word-text {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.word-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.has-video,
.no-video,
.study-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  width: fit-content;
}

.has-video {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.no-video {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.study-count {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

/* Word actions removed - no longer needed on word cards */

/* No Results */
.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.no-results i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1000;
  padding: 2rem;
  overflow-y: auto;
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition);
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  border-top: 1px solid var(--border-color);
}

/* Video Container */
.video-container {
  margin-bottom: 2rem;
}

.video-container video {
  width: 100%;
  border-radius: var(--border-radius);
  background: #000;
}

.no-video-message {
  text-align: center;
  padding: 3rem 2rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
}

.no-video-message i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.upload-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Video Upload Section */
.video-upload-section {
  margin-bottom: 2rem;
  padding: 2rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.video-upload-section h4 {
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.upload-area {
  position: relative;
  margin-bottom: 1.5rem;
}

.upload-area input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  background: var(--bg-card);
}

.upload-label:hover {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.upload-label i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.upload-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Word Details */
.word-details {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.detail-item label {
  font-weight: 600;
  color: var(--text-primary);
}

.detail-item p {
  color: var(--text-secondary);
  margin: 0;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-family: inherit;
  transition: var(--transition);
  background: var(--bg-card);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group small {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* Current video section in edit modal */
#currentVideoSection {
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

#currentEditVideo {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

#noCurrentVideo {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
  font-style: italic;
}

/* Delete video button */
.delete-video-btn {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: inherit;
}

.delete-video-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.delete-video-btn:active {
  transform: translateY(0);
}

#videoActions {
  display: flex;
  justify-content: center;
}

/* Video Input Options */
.video-input-options {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  background: var(--bg-secondary);
}

.video-option-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.video-option-btn {
  flex: 1;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: inherit;
}

.video-option-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.video-option-btn.active {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

/* Video Recording Section */
.video-recording-section {
  margin-top: 1rem;
}

.recording-container {
  position: relative;
  background: #000;
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-bottom: 1rem;
}

.camera-preview,
.recorded-preview {
  width: 100%;
  aspect-ratio: 1 / 1;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  display: block;
}

/* Fix mirrored camera preview - flip horizontally to show true view */
.camera-preview {
  transform: scaleX(-1);
}

/* Ensure recorded videos are not mirrored (they should show the true orientation) */
.recorded-preview {
  transform: none;
}

/* Infinite Scroll Loading Indicator */
.infinite-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  margin: 1rem 0;
}

.loading-spinner {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.loading-spinner i {
  font-size: 1.2rem;
  color: var(--primary-color);
}

.loading-spinner span {
  font-weight: 500;
}

/* Scroll sentinel (invisible element for intersection observer) */
#scroll-sentinel {
  height: 1px;
  width: 100%;
  pointer-events: none;
}

.recording-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.record-btn {
  background: var(--bg-card);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.record-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.record-btn i {
  font-size: 1.5rem;
}

.record-btn.recording {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
  animation: pulse 1s infinite;
}

.record-btn i.fa-circle {
  color: var(--error-color);
}

.record-btn.stop {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.record-btn.stop:hover {
  background: #dc2626;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.recording-status {
  text-align: center;
  font-weight: 600;
  color: var(--text-primary);
  padding: 0.5rem;
  background: var(--bg-card);
  border-radius: var(--border-radius);
}

.recording-status.recording {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

/* Video Selection Section */
.video-selection-section {
  margin-top: 1rem;
}

.video-selection-section input[type="file"] {
  width: 100%;
  padding: 1rem;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-card);
  cursor: pointer;
  transition: var(--transition);
}

.video-selection-section input[type="file"]:hover {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

/* Video Cropping Interface */
.video-cropper-section {
  margin-top: 1.5rem;
  padding: 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-card);
}

.video-cropper-section h5 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-weight: 600;
  text-align: center;
}

.cropper-container {
  position: relative;
  background: #000;
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-bottom: 1rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.cropper-video {
  width: 100%;
  height: 400px;
  display: block;
  object-fit: contain;
  background: #000;
}

.crop-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.crop-selection {
  position: absolute;
  border: 3px solid var(--primary-color);
  border-radius: 8px;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5),
    inset 0 0 0 2px rgba(255, 255, 255, 0.8);
  pointer-events: auto;
  cursor: move;
  min-width: 50px;
  min-height: 50px;
}

.crop-selection::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid white;
  border-radius: 50%;
  background: var(--primary-color);
}

.crop-selection::after {
  content: "بڕین";
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Resize Handles */
.resize-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  border: 2px solid white;
  border-radius: 50%;
  z-index: 10;
}

.resize-handle:hover {
  background: var(--secondary-color);
  transform: scale(1.2);
}

.resize-se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

.resize-sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.resize-ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.cropper-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.crop-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: inherit;
}

.crop-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.crop-btn.secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.crop-btn.secondary:hover {
  background: linear-gradient(135deg, #4b5563, #374151);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Button Styles */
.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.btn-danger {
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-header {
    flex-direction: column;
    align-items: stretch;
  }

  /* Removed conflicting words-grid rule - using the 2-column rule from above */

  .word-card {
    flex-direction: column;
    gap: 1rem;
  }

  /* Word actions removed - no longer needed */

  .modal {
    padding: 1rem;
  }

  .modal-content {
    max-height: 95vh;
    width: calc(100vw - 2rem);
    max-width: 600px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .form-actions,
  .upload-actions,
  .modal-footer {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .search-section {
    padding: 1rem;
    margin: 0 -1rem 2rem;
    border-radius: 0;
  }

  .words-section {
    padding: 1rem;
    margin: 0 -1rem;
    border-radius: 0;
  }

  .modal-content {
    width: calc(100vw - 1rem);
    margin: 0.5rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .search-input-group {
    margin: 0 -1rem;
    border-radius: 0;
  }

  .word-card {
    margin: 0 -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .words-grid {
    gap: 0.5rem; /* Keep some gap for readability */
    margin: 0 -1rem;
    /* Keep the 2-column layout from the main mobile rule */
  }

  .search-header {
    padding: 0 1rem;
  }

  .add-word-btn {
    width: 100%;
    justify-content: center;
  }
}

<?php
// Shared header component with user info and navigation
if (!function_exists('getCurrentUser')) {
    require_once __DIR__ . '/../config.php';
}

// Get current user if not already set
if (!isset($currentUser)) {
    $currentUser = getCurrentUser();
}

// Get current page for navigation highlighting
$currentPage = basename($_SERVER['PHP_SELF'], '.php');

// Get current language info
$currentLang = getCurrentLanguage();
$langDirection = getLanguageDirection();
$supportedLanguages = getSupportedLanguages();
?>

<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $langDirection; ?>">
<head>
    <script>
        // Set initial dark mode before page renders
        (function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.documentElement.classList.add('dark-mode');
                document.body.classList.add('dark-mode');
            }
        })();
    </script>
</head>
<body>

<!-- Header -->
<header class="header">
    <div class="header-content">
        <h1 class="logo">
            <img src="assets/images/dastare_logo.png" alt="<?php echo __('app_title'); ?>" class="logo-image">
            <span class="logo-text"><?php echo __('app_title'); ?></span>
        </h1>
        <p class="subtitle"><?php echo isset($pageSubtitle) ? $pageSubtitle : __('app_subtitle'); ?></p>
    </div>
    <div class="user-info">
        <div class="user-details">
            <span class="user-greeting"><?php echo __('user_greeting', htmlspecialchars($currentUser['name'])); ?></span>
            <span class="user-email"><?php echo htmlspecialchars($currentUser['email']); ?></span>
        </div>
        <div class="header-actions">
            <div class="language-selector">
                <button class="language-btn" onclick="toggleLanguageMenu()" title="<?php echo __('language'); ?>">
                    <i class="fas fa-globe"></i>
                    <span class="current-lang"><?php echo getLanguageName(); ?></span>
                </button>
                <div class="language-menu" id="languageMenu">
                    <?php foreach ($supportedLanguages as $code => $info): ?>
                    <a href="#" onclick="changeLanguage('<?php echo $code; ?>')" class="lang-option <?php echo $code === $currentLang ? 'active' : ''; ?>">
                        <span class="lang-flag"><?php echo $info['flag']; ?></span>
                        <span><?php echo $info['name']; ?></span>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <button class="settings-btn" onclick="toggleSettings()" title="<?php echo __('settings'); ?>">
                <i class="fas fa-cog"></i>
            </button>
            <button class="notifications-btn" onclick="toggleNotifications()" title="<?php echo __('notifications'); ?>">
                <i class="fas fa-bell"></i>
                <span class="notification-badge" id="notificationBadge" style="display: none;">3</span>
            </button>
        </div>
    </div>
</header>

<!-- User Menu & Settings -->
<div class="user-menu-overlay" id="userMenuOverlay" onclick="closeAllMenus()"></div>

<!-- Settings Panel -->
<div class="settings-panel" id="settingsPanel">
    <div class="panel-header">
        <h3><i class="fas fa-cog"></i> <?php echo __('settings'); ?></h3>
        <button class="close-btn" onclick="toggleSettings()">×</button>
    </div>
    <div class="panel-content">
        <div class="setting-item">
            <label for="darkMode"><?php echo __('dark_mode'); ?></label>
            <input type="checkbox" id="darkMode" onchange="toggleDarkMode()">
        </div>
        <div class="setting-item">
            <label for="notifications"><?php echo __('notifications'); ?></label>
            <input type="checkbox" id="notifications" checked onchange="toggleNotificationSettings()">
        </div>
        <div class="setting-item">
            <label for="autoPlay"><?php echo __('auto_play'); ?></label>
            <input type="checkbox" id="autoPlay" checked onchange="toggleAutoPlay()">
        </div>
        <div class="setting-item">
            <label for="studyReminder"><?php echo __('study_reminder'); ?></label>
            <select id="studyReminder" onchange="setStudyReminder()">
                <option value="off"><?php echo __('cancel'); ?></option>
                <option value="daily" selected><?php echo __('today'); ?></option>
                <option value="weekly"><?php echo __('this_week'); ?></option>
            </select>
        </div>
    </div>
</div>

<!-- Notifications Panel -->
<div class="notifications-panel" id="notificationsPanel">
    <div class="panel-header">
        <h3><i class="fas fa-bell"></i> <?php echo __('notifications'); ?></h3>
        <button class="close-btn" onclick="toggleNotifications()">×</button>
    </div>
    <div class="panel-content">
        <p style="text-align: center; color: var(--text-secondary); padding: 2rem;"><?php echo __('no_notifications'); ?></p>
    </div>
</div>

<!-- Bottom Action Bar -->
<div class="bottom-action-bar">
    <div class="action-bar-content">
        <button class="action-btn" onclick="showProfile()">
            <i class="fas fa-user"></i>
            <span><?php echo __('profile'); ?></span>
        </button>
        <button class="action-btn" onclick="showHelp()">
            <i class="fas fa-question-circle"></i>
            <span><?php echo __('help'); ?></span>
        </button>
        <button class="action-btn" onclick="exportData()">
            <i class="fas fa-download"></i>
            <span><?php echo __('export'); ?></span>
        </button>
        <button class="action-btn logout-btn" onclick="confirmLogout()">
            <i class="fas fa-sign-out-alt"></i>
            <span><?php echo __('logout'); ?></span>
        </button>
    </div>
</div>

<?php
require_once 'config.php';

// Require authentication
requireAuth();

// Get current user
$currentUser = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getLanguageDirection(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('help'); ?> - <?php echo __('app_title'); ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        // Set initial dark mode before page renders
        (function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.documentElement.classList.add('dark-mode');
                document.body.classList.add('dark-mode');
            }
        })();
    </script>
    <script src="js/shared.js"></script>
    <style>
        .help-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .help-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .help-sections {
            display: grid;
            gap: 1.5rem;
        }
        
        .help-section {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            overflow: hidden;
        }
        
        .section-header {
            background: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
        }
        
        .section-header:hover {
            background: var(--secondary-color);
        }
        
        .section-content {
            padding: 1.5rem;
            display: none;
        }
        
        .section-content.active {
            display: block;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .step-list li:last-child {
            border-bottom: none;
        }
        
        .step-number {
            background: var(--primary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .faq-item {
            margin-bottom: 1rem;
        }
        
        .faq-question {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .faq-answer {
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .contact-info {
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-top: 2rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .contact-item:last-child {
            margin-bottom: 0;
        }
        
        .contact-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php
        $pageSubtitle = __('help');
        include 'includes/header.php';
        ?>

        <div class="help-container">
            <div class="help-header">
                <h1><i class="fas fa-question-circle"></i> یارمەتی و ڕێنمایی</h1>
                <p>هەموو ئەوەی پێویستتە بۆ بەکارهێنانی دەستەڕێ</p>
            </div>
            
            <div class="help-sections">
                <div class="help-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-play-circle"></i> چۆن دەست پێ بکەم؟</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="section-content">
                        <ol class="step-list">
                            <li>
                                <div class="step-number">1</div>
                                <div>
                                    <strong>چوونەژوورەوە:</strong> بە هەژماری گووگڵەکەت بچۆرە ژوورەوە
                                </div>
                            </li>
                            <li>
                                <div class="step-number">2</div>
                                <div>
                                    <strong>گەڕان لە وشەکان:</strong> لە بەشی "گەڕان" وشەکان ببینە
                                </div>
                            </li>
                            <li>
                                <div class="step-number">3</div>
                                <div>
                                    <strong>دەستپێکردنی خوێندن:</strong> لە "خوێندن" کلیک بکە بۆ فێربوونی وشەکان
                                </div>
                            </li>
                            <li>
                                <div class="step-number">4</div>
                                <div>
                                    <strong>بەدواداچوونی پێشکەوتن:</strong> لە "پێشکەوتن" ئامارەکانت ببینە
                                </div>
                            </li>
                        </ol>
                    </div>
                </div>
                
                <div class="help-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-graduation-cap"></i> چۆن فێر دەبم؟</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="section-content">
                        <ol class="step-list">
                            <li>
                                <div class="step-number">1</div>
                                <div>
                                    <strong>ڤیدیۆ تەماشا بکە:</strong> ڤیدیۆی ئاماژەکە بە وردی تەماشا بکە
                                </div>
                            </li>
                            <li>
                                <div class="step-number">2</div>
                                <div>
                                    <strong>ئاماژەکە دووبارە بکەرەوە:</strong> هەوڵ بدە ئاماژەکە بکەیت
                                </div>
                            </li>
                            <li>
                                <div class="step-number">3</div>
                                <div>
                                    <strong>وەڵام بدەرەوە:</strong> "بیرمە" یان "لەبیرچووم" هەڵبژێرە
                                </div>
                            </li>
                            <li>
                                <div class="step-number">4</div>
                                <div>
                                    <strong>دووبارە بکەرەوە:</strong> وشەکانی لەبیرچوو دووبارە دەکرێنەوە
                                </div>
                            </li>
                        </ol>
                    </div>
                </div>
                
                <div class="help-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-cog"></i> ڕێکخستنەکان</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="section-content">
                        <ol class="step-list">
                            <li>
                                <div class="step-number">1</div>
                                <div>
                                    <strong>دۆخی تاریک:</strong> بۆ خوێندن لە شەودا چالاکی بکە
                                </div>
                            </li>
                            <li>
                                <div class="step-number">2</div>
                                <div>
                                    <strong>ئاگادارکردنەوەکان:</strong> بیرخستنەوەی خوێندن وەربگرە
                                </div>
                            </li>
                            <li>
                                <div class="step-number">3</div>
                                <div>
                                    <strong>پەخشی خۆکار:</strong> ڤیدیۆکان خۆکار پەخش بکرێن
                                </div>
                            </li>
                            <li>
                                <div class="step-number">4</div>
                                <div>
                                    <strong>بیرخستنەوەی خوێندن:</strong> ڕۆژانە یان هەفتانە دابنێ
                                </div>
                            </li>
                        </ol>
                    </div>
                </div>
                
                <div class="help-section">
                    <div class="section-header" onclick="toggleSection(this)">
                        <h3><i class="fas fa-question"></i> پرسیارە دووبارەکان</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="section-content">
                        <div class="faq-item">
                            <div class="faq-question">چۆن وشەی نوێ زیاد بکەم؟</div>
                            <div class="faq-answer">لە بەشی گەڕان، دوگمەی "زیادکردنی وشەی نوێ" کلیک بکە و فۆڕمەکە پڕ بکەرەوە.</div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">چۆن پێشکەوتنم ببینم؟</div>
                            <div class="faq-answer">لە بەشی "پێشکەوتن" دەتوانیت چارت و ئامارەکانی فێربوونت ببینیت.</div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">چۆن زانیارییەکانم هەناردە بکەم؟</div>
                            <div class="faq-answer">لە خوارەوەی پەڕە، دوگمەی "هەناردەکردن" کلیک بکە بۆ داگرتنی زانیارییەکانت.</div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question">ئایا دەتوانم بەبێ ئینتەرنێت بەکاری بهێنم؟</div>
                            <div class="faq-answer">بەڵێ، پاش یەکەم جار بارکردن، زۆربەی تایبەتمەندییەکان بەبێ ئینتەرنێت کار دەکەن.</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="contact-info">
                <h3><i class="fas fa-envelope"></i> پەیوەندی</h3>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div>
                        <strong>ئیمەیڵ:</strong> <EMAIL>
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fab fa-telegram"></i>
                    </div>
                    <div>
                        <strong>تێلێگرام:</strong> @DastareSupport
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div>
                        <strong>ماڵپەڕ:</strong> www.dastare.com
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i> گەڕانەوە بۆ سەرەکی
                </a>
            </div>
        </div>
    </div>
    
    <script>
        function toggleSection(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.fa-chevron-down, .fa-chevron-up');

            // Close all other sections
            document.querySelectorAll('.section-content').forEach(section => {
                if (section !== content) {
                    section.classList.remove('active');
                }
            });

            document.querySelectorAll('.section-header i:last-child').forEach(i => {
                if (i !== icon) {
                    i.className = 'fas fa-chevron-down';
                }
            });

            // Toggle current section
            content.classList.toggle('active');
            icon.className = content.classList.contains('active') ? 'fas fa-chevron-up' : 'fas fa-chevron-down';
        }

        // Initialize dark mode on page load
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.body.classList.add('dark-mode');
                document.documentElement.classList.add('dark-mode');
            }
        });
    </script>
</body>
</html>

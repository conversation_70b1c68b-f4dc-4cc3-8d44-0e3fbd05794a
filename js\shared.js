// Shared JavaScript functionality for all pages

// Language translations
const translations = {
  ckb: {
    welcome: "بەخێربێیت",
    profile: "پڕۆفایل",
    help: "یارمەتی",
    export: "هەناردەکردن",
    logout: "دەرچوون",
    settings: "ڕێکخستنەکان",
    notifications: "ئاگادارکردنەوەکان",
    darkMode: "دۆخی تاریک",
    autoPlay: "پەخشی خۆکار بۆ ڤیدیۆکان",
    studyReminder: "بیرخستنەوەی خوێندن",
    exportFormat: "فۆڕماتی هەناردەکردن هەڵبژێرە:\nOK = JSON\nCancel = CSV",
    exportStarting: "هەناردەکردنی زانیاری دەست پێدەکات...",
    exportSuccess: "زانیاری بە سەرکەوتوویی هەناردەکرا",
    exportError: "هەڵەیەک ڕوویدا لە هەناردەکردن",
    logoutConfirm: "ئایا دڵنیایت لە دەرچوون؟",
    darkModeEnabled: "دۆخی تاریک چالاککرا",
    darkModeDisabled: "دۆخی ڕووناک چالاککرا",
  },
  ar: {
    welcome: "مرحباً",
    profile: "الملف الشخصي",
    help: "المساعدة",
    export: "تصدير",
    logout: "تسجيل الخروج",
    settings: "الإعدادات",
    notifications: "الإشعارات",
    darkMode: "الوضع المظلم",
    autoPlay: "التشغيل التلقائي للفيديو",
    studyReminder: "تذكير الدراسة",
    exportFormat: "اختر تنسيق التصدير:\nOK = JSON\nCancel = CSV",
    exportStarting: "بدء تصدير البيانات...",
    exportSuccess: "تم تصدير البيانات بنجاح",
    exportError: "حدث خطأ في التصدير",
    logoutConfirm: "هل أنت متأكد من تسجيل الخروج؟",
    darkModeEnabled: "تم تفعيل الوضع المظلم",
    darkModeDisabled: "تم تفعيل الوضع المضيء",
  },
  en: {
    welcome: "Welcome",
    profile: "Profile",
    help: "Help",
    export: "Export",
    logout: "Logout",
    settings: "Settings",
    notifications: "Notifications",
    darkMode: "Dark Mode",
    autoPlay: "Auto-play Videos",
    studyReminder: "Study Reminder",
    exportFormat: "Choose export format:\nOK = JSON\nCancel = CSV",
    exportStarting: "Starting data export...",
    exportSuccess: "Data exported successfully",
    exportError: "Error occurred during export",
    logoutConfirm: "Are you sure you want to logout?",
    darkModeEnabled: "Dark mode enabled",
    darkModeDisabled: "Light mode enabled",
  },
};

// Current language
let currentLang = localStorage.getItem("language") || "ckb";

// Get translation
function t(key) {
  return translations[currentLang][key] || translations.ckb[key] || key;
}

// Language Management
function toggleLanguageMenu() {
  const menu = document.getElementById("languageMenu");
  menu.classList.toggle("show");
}

async function changeLanguage(lang) {
  try {
    // Get the correct API path
    const currentPath = window.location.pathname;
    const apiPath = currentPath.includes("/auth/")
      ? "../api/change_language.php"
      : "api/change_language.php";

    const response = await fetch(apiPath, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: `language=${lang}`,
    });

    const data = await response.json();

    if (data.success) {
      // Update localStorage
      localStorage.setItem("language", lang);
      currentLang = lang;

      // Update UI
      updateUILanguage();

      // Close language menu
      document.getElementById("languageMenu").classList.remove("show");

      // Show success message
      showToast(`Language changed to ${data.name}`, "success");

      // Reload page to apply server-side translations
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error("Language change error:", error);
    showToast("Failed to change language: " + error.message, "error");
  }
}

function updateUILanguage() {
  // Update current language display
  const currentLangSpan = document.querySelector(".current-lang");
  const langNames = { ckb: "کوردی", ar: "العربية", en: "English" };
  if (currentLangSpan) {
    currentLangSpan.textContent = langNames[currentLang];
  }

  // Update document direction
  document.documentElement.dir =
    currentLang === "ar" || currentLang === "ckb" ? "rtl" : "ltr";
  document.documentElement.lang = currentLang;

  // Update action bar text
  const actionBtns = document.querySelectorAll(".action-btn span");
  if (actionBtns.length >= 4) {
    actionBtns[0].textContent = t("profile");
    actionBtns[1].textContent = t("help");
    actionBtns[2].textContent = t("export");
    actionBtns[3].textContent = t("logout");
  }

  // Update active language option
  document.querySelectorAll(".lang-option").forEach((option) => {
    option.classList.remove("active");
  });
  document
    .querySelector(`[onclick="changeLanguage('${currentLang}')"]`)
    .classList.add("active");
}

// Settings and Notifications Management
function toggleSettings() {
  const panel = document.getElementById("settingsPanel");
  const overlay = document.getElementById("userMenuOverlay");
  const isOpen = panel.classList.contains("open");

  closeAllMenus();

  if (!isOpen) {
    panel.classList.add("open");
    overlay.classList.add("active");
    document.body.style.overflow = "hidden";
  }
}

function toggleNotifications() {
  const panel = document.getElementById("notificationsPanel");
  const overlay = document.getElementById("userMenuOverlay");
  const badge = document.getElementById("notificationBadge");
  const isOpen = panel.classList.contains("open");

  closeAllMenus();

  if (!isOpen) {
    panel.classList.add("open");
    overlay.classList.add("active");
    document.body.style.overflow = "hidden";
    if (badge) badge.style.display = "none";

    // Load notifications
    loadNotifications();
  }
}

function closeAllMenus() {
  const panels = document.querySelectorAll(
    ".settings-panel, .notifications-panel"
  );
  const overlay = document.getElementById("userMenuOverlay");
  const langMenu = document.getElementById("languageMenu");

  panels.forEach((panel) => panel.classList.remove("open"));
  if (overlay) overlay.classList.remove("active");
  if (langMenu) langMenu.classList.remove("show");
  document.body.style.overflow = "";
}

// Settings Functions
function toggleDarkMode() {
  const isDark = document.getElementById("darkMode").checked;
  document.body.classList.toggle("dark-mode", isDark);
  document.documentElement.classList.toggle("dark-mode", isDark);
  localStorage.setItem("darkMode", isDark);
  showToast(isDark ? t("darkModeEnabled") : t("darkModeDisabled"));
}

function toggleNotificationSettings() {
  const enabled = document.getElementById("notifications").checked;
  localStorage.setItem("notificationsEnabled", enabled);
  showToast(enabled ? "Notifications enabled" : "Notifications disabled");
}

function toggleAutoPlay() {
  const enabled = document.getElementById("autoPlay").checked;
  localStorage.setItem("autoPlay", enabled);
  showToast(enabled ? "Auto-play enabled" : "Auto-play disabled");
}

function setStudyReminder() {
  const value = document.getElementById("studyReminder").value;
  localStorage.setItem("studyReminder", value);
  const messages = {
    off: "Study reminders disabled",
    daily: "Daily reminders set",
    weekly: "Weekly reminders set",
  };
  showToast(messages[value]);
}

// Action Functions
function showProfile() {
  // Get the correct path based on current location
  const currentPath = window.location.pathname;
  const profilePath = currentPath.includes("/auth/")
    ? "../profile.php"
    : "profile.php";
  window.location.href = profilePath;
}

function showHelp() {
  const currentPath = window.location.pathname;
  const helpPath = currentPath.includes("/auth/") ? "../help.php" : "help.php";
  window.location.href = helpPath;
}

async function exportData() {
  const format = confirm(t("exportFormat")) ? "json" : "csv";

  try {
    showToast(t("exportStarting"), "info");

    // Get the correct API path
    const currentPath = window.location.pathname;
    const apiPath = currentPath.includes("/auth/")
      ? "../api/export_data.php"
      : "api/export_data.php";

    const response = await fetch(`${apiPath}?format=${format}`);

    if (response.ok) {
      const contentType = response.headers.get("content-type");

      // Check if it's a file download or error response
      if (
        contentType &&
        contentType.includes("application/json") &&
        !response.headers.get("content-disposition")
      ) {
        // It's an error response
        const errorData = await response.json();
        throw new Error(errorData.message || t("exportError"));
      } else {
        // It's a file download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `dastare_data_${
          new Date().toISOString().split("T")[0]
        }.${format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showToast(t("exportSuccess"), "success");
      }
    } else {
      // Try to get error message from response
      try {
        const errorData = await response.json();
        throw new Error(errorData.message || t("exportError"));
      } catch {
        throw new Error(`HTTP ${response.status}: ${t("exportError")}`);
      }
    }
  } catch (error) {
    console.error("Export error:", error);
    showToast(t("exportError") + ": " + error.message, "error");
  }
}

// Test export function
async function testExportData() {
  const format = confirm("Test Export Format:\nOK = JSON\nCancel = CSV")
    ? "json"
    : "csv";

  try {
    showToast("Starting test export...", "info");

    const currentPath = window.location.pathname;
    const apiPath = currentPath.includes("/auth/")
      ? "../api/test_export.php"
      : "api/test_export.php";

    const response = await fetch(`${apiPath}?format=${format}`);

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `dastare_test_${
        new Date().toISOString().split("T")[0]
      }.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      showToast("Test export successful!", "success");
    } else {
      const errorData = await response.json();
      throw new Error(errorData.message || "Export failed");
    }
  } catch (error) {
    console.error("Test export error:", error);
    showToast("Test export failed: " + error.message, "error");
  }
}

function confirmLogout() {
  if (confirm(t("logoutConfirm"))) {
    const currentPath = window.location.pathname;
    const logoutPath = currentPath.includes("/auth/")
      ? "logout.php"
      : "auth/logout.php";
    window.location.href = logoutPath;
  }
}

// Footer link functions
function showPrivacyPolicy() {
  const currentPath = window.location.pathname;
  const privacyPath = currentPath.includes("/auth/")
    ? "../privacy.php"
    : "privacy.php";
  window.open(privacyPath, "_blank");
}

function showTerms() {
  const currentPath = window.location.pathname;
  const termsPath = currentPath.includes("/auth/")
    ? "../terms.php"
    : "terms.php";
  window.open(termsPath, "_blank");
}

function showContact() {
  const currentPath = window.location.pathname;
  const contactPath = currentPath.includes("/auth/")
    ? "../contact.php"
    : "contact.php";
  window.open(contactPath, "_blank");
}

// Load notifications
async function loadNotifications() {
  try {
    const currentPath = window.location.pathname;
    const apiPath = currentPath.includes("/auth/")
      ? "../api/notifications.php"
      : "api/notifications.php";

    const response = await fetch(`${apiPath}?action=get`);
    const data = await response.json();

    if (data.success) {
      updateNotificationPanel(data.notifications);
      updateNotificationBadge(data.unread_count);
    }
  } catch (error) {
    console.error("Error loading notifications:", error);
  }
}

function updateNotificationPanel(notifications) {
  const panel = document.querySelector(".notifications-panel .panel-content");
  if (!panel) return;

  if (notifications.length === 0) {
    panel.innerHTML =
      '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">No notifications</p>';
    return;
  }

  panel.innerHTML = notifications
    .map(
      (notification) => `
        <div class="notification-item ${
          notification.unread ? "unread" : ""
        }" data-id="${notification.id}">
            <div class="notification-icon">
                <i class="${notification.icon}"></i>
            </div>
            <div class="notification-content">
                <h4>${notification.title}</h4>
                <p>${notification.message}</p>
                <span class="notification-time">${notification.time}</span>
            </div>
        </div>
    `
    )
    .join("");

  // Add click handlers to mark as read
  panel.querySelectorAll(".notification-item.unread").forEach((item) => {
    item.addEventListener("click", function () {
      markNotificationAsRead(this.dataset.id);
      this.classList.remove("unread");
      updateNotificationBadge();
    });
  });
}

function updateNotificationBadge(count = null) {
  const badge = document.getElementById("notificationBadge");
  if (!badge) return;

  if (count === null) {
    count = document.querySelectorAll(".notification-item.unread").length;
  }

  if (count > 0) {
    badge.textContent = count;
    badge.style.display = "block";
  } else {
    badge.style.display = "none";
  }
}

function markNotificationAsRead(notificationId) {
  const readNotifications = JSON.parse(
    localStorage.getItem("readNotifications") || "[]"
  );
  if (!readNotifications.includes(notificationId)) {
    readNotifications.push(notificationId);
    localStorage.setItem(
      "readNotifications",
      JSON.stringify(readNotifications)
    );
  }
}

// Toast Notification System
function showToast(message, type = "info") {
  const toast = document.createElement("div");
  toast.className = `toast toast-${type}`;
  toast.innerHTML = `
        <i class="fas fa-${
          type === "success" ? "check" : type === "error" ? "times" : "info"
        }-circle"></i>
        <span>${message}</span>
    `;

  document.body.appendChild(toast);

  setTimeout(() => toast.classList.add("show"), 100);
  setTimeout(() => {
    toast.classList.remove("show");
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, 3000);
}

// Initialize on page load
document.addEventListener("DOMContentLoaded", function () {
  // Load saved settings
  const darkMode = localStorage.getItem("darkMode") === "true";
  const darkModeCheckbox = document.getElementById("darkMode");
  if (darkModeCheckbox) {
    darkModeCheckbox.checked = darkMode;
    document.body.classList.toggle("dark-mode", darkMode);
    document.documentElement.classList.toggle("dark-mode", darkMode);
  }

  const notificationsCheckbox = document.getElementById("notifications");
  if (notificationsCheckbox) {
    notificationsCheckbox.checked =
      localStorage.getItem("notificationsEnabled") !== "false";
  }

  const autoPlayCheckbox = document.getElementById("autoPlay");
  if (autoPlayCheckbox) {
    autoPlayCheckbox.checked = localStorage.getItem("autoPlay") !== "false";
  }

  const studyReminderSelect = document.getElementById("studyReminder");
  if (studyReminderSelect) {
    studyReminderSelect.value =
      localStorage.getItem("studyReminder") || "daily";
  }

  // Update UI language
  updateUILanguage();

  // Load notifications
  loadNotifications();
});

// Close menus on escape key
document.addEventListener("keydown", function (e) {
  if (e.key === "Escape") {
    closeAllMenus();
  }
});

// Close language menu when clicking outside
document.addEventListener("click", function (e) {
  if (!e.target.closest(".language-selector")) {
    const langMenu = document.getElementById("languageMenu");
    if (langMenu) langMenu.classList.remove("show");
  }
});

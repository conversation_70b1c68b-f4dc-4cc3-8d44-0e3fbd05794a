// Progress Page JavaScript
// Handle charts and progress visualization

let weeklyChart = null;
let successChart = null;

// Initialize progress page
document.addEventListener("DOMContentLoaded", function () {
  initializeProgressPage();
});

function initializeProgressPage() {
  initializeCharts();
  setupProgressHandlers();
}

// Initialize charts
function initializeCharts() {
  createWeeklyChart();
  createSuccessChart();
}

// Create weekly progress chart
function createWeeklyChart() {
  const ctx = document.getElementById("weeklyChart");
  if (!ctx) return;

  // Prepare data from PHP
  const labels = [];
  const studiedData = [];
  const rememberedData = [];
  const forgotData = [];

  // Process weekly data (reverse to show chronologically)
  const sortedData = weeklyData.slice().reverse();

  sortedData.forEach((day) => {
    const date = new Date(day.study_date);
    labels.push(
      date.toLocaleDateString("ckb-IQ", {
        month: "short",
        day: "numeric",
      })
    );
    studiedData.push(parseInt(day.words_studied));
    rememberedData.push(parseInt(day.words_remembered));
    forgotData.push(parseInt(day.words_forgot));
  });

  // Fill missing days with zeros
  while (labels.length < 7) {
    const date = new Date();
    date.setDate(date.getDate() - (7 - labels.length));
    labels.unshift(
      date.toLocaleDateString("ckb-IQ", {
        month: "short",
        day: "numeric",
      })
    );
    studiedData.unshift(0);
    rememberedData.unshift(0);
    forgotData.unshift(0);
  }

  weeklyChart = new Chart(ctx, {
    type: "line",
    data: {
      labels: labels,
      datasets: [
        {
          label: "وشەی خوێندراو",
          data: studiedData,
          borderColor: "#667eea",
          backgroundColor: "rgba(102, 126, 234, 0.1)",
          tension: 0.4,
          fill: true,
        },
        {
          label: "بیرکراوە",
          data: rememberedData,
          borderColor: "#10b981",
          backgroundColor: "rgba(16, 185, 129, 0.1)",
          tension: 0.4,
          fill: true,
        },
        {
          label: "لەبیرکراوە",
          data: forgotData,
          borderColor: "#ef4444",
          backgroundColor: "rgba(239, 68, 68, 0.1)",
          tension: 0.4,
          fill: true,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: "top",
          labels: {
            usePointStyle: true,
            font: {
              family: "Noto Sans Arabic",
            },
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1,
            font: {
              family: "Noto Sans Arabic",
            },
          },
        },
        x: {
          ticks: {
            font: {
              family: "Noto Sans Arabic",
            },
          },
        },
      },
      elements: {
        point: {
          radius: 4,
          hoverRadius: 6,
        },
      },
    },
  });
}

// Create success rate chart
function createSuccessChart() {
  const ctx = document.getElementById("successChart");
  if (!ctx) return;

  // Calculate success rate data
  const labels = [];
  const successRates = [];

  const sortedData = weeklyData.slice().reverse();

  sortedData.forEach((day) => {
    const date = new Date(day.study_date);
    labels.push(
      date.toLocaleDateString("ckb-IQ", {
        month: "short",
        day: "numeric",
      })
    );
    successRates.push(parseFloat(day.success_rate) || 0);
  });

  // Fill missing days
  while (labels.length < 7) {
    const date = new Date();
    date.setDate(date.getDate() - (7 - labels.length));
    labels.unshift(
      date.toLocaleDateString("ckb-IQ", {
        month: "short",
        day: "numeric",
      })
    );
    successRates.unshift(0);
  }

  successChart = new Chart(ctx, {
    type: "bar",
    data: {
      labels: labels,
      datasets: [
        {
          label: "ڕێژەی سەرکەوتن (%)",
          data: successRates,
          backgroundColor: successRates.map((rate) => {
            if (rate >= 80) return "rgba(16, 185, 129, 0.8)";
            if (rate >= 60) return "rgba(245, 158, 11, 0.8)";
            return "rgba(239, 68, 68, 0.8)";
          }),
          borderColor: successRates.map((rate) => {
            if (rate >= 80) return "#10b981";
            if (rate >= 60) return "#f59e0b";
            return "#ef4444";
          }),
          borderWidth: 2,
          borderRadius: 4,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          ticks: {
            callback: function (value) {
              return value + "%";
            },
            font: {
              family: "Noto Sans Arabic",
            },
          },
        },
        x: {
          ticks: {
            font: {
              family: "Noto Sans Arabic",
            },
          },
        },
      },
    },
  });
}

// Setup progress page handlers
function setupProgressHandlers() {
  // Add any additional event handlers here
}

// Practice specific word
function practiceWord(word) {
  // Store the word to practice and redirect to study page
  sessionStorage.setItem("practiceWord", word);
  window.location.href = "study.php?mode=practice";
}

// Export progress data
function exportProgress() {
  try {
    // Prepare data for export
    const exportData = {
      exportDate: new Date().toISOString(),
      weeklyProgress: weeklyData,
      monthlyProgress: monthlyData,
      summary: {
        totalDays: weeklyData.length,
        totalWordsStudied: weeklyData.reduce(
          (sum, day) => sum + parseInt(day.words_studied),
          0
        ),
        totalRemembered: weeklyData.reduce(
          (sum, day) => sum + parseInt(day.words_remembered),
          0
        ),
        totalForgot: weeklyData.reduce(
          (sum, day) => sum + parseInt(day.words_forgot),
          0
        ),
        totalTimeSpent: weeklyData.reduce(
          (sum, day) => sum + parseInt(day.study_time_minutes),
          0
        ),
      },
    };

    // Create and download JSON file
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });

    const link = document.createElement("a");
    link.href = URL.createObjectURL(dataBlob);
    link.download = `dastare-progress-${
      new Date().toISOString().split("T")[0]
    }.json`;
    link.click();

    DastareApp.showNotification(
      "ئامارەکان بە سەرکەوتوویی دەرهێنران",
      "success"
    );
  } catch (error) {
    console.error("Export error:", error);
    DastareApp.showNotification(
      "هەڵەیەک ڕوویدا لە دەرهێنانی ئامارەکان",
      "error"
    );
  }
}

// Reset progress data
async function resetProgress() {
  if (!confirm("دڵنیای لە سڕینەوەی هەموو ئامارەکان؟ ئەم کردارە ناگەڕێتەوە!")) {
    return;
  }

  if (
    !confirm(
      "دووبارە دڵنیابوونەوە: هەموو پێشکەوتن و ئامارەکان دەسڕێنەوە. دەتەوێت بەردەوام بیت؟"
    )
  ) {
    return;
  }

  try {
    DastareApp.showLoading(true);

    const response = await fetch("api/reset_progress.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (data.success) {
      DastareApp.showNotification(
        "ئامارەکان بە سەرکەوتوویی سڕانەوە",
        "success"
      );

      // Reload page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } else {
      DastareApp.showNotification(data.message || "هەڵەیەک ڕوویدا", "error");
    }
  } catch (error) {
    console.error("Reset error:", error);
    DastareApp.showNotification(
      "هەڵەیەک ڕوویدا لە سڕینەوەی ئامارەکان",
      "error"
    );
  } finally {
    DastareApp.showLoading(false);
  }
}

// Update charts when data changes
function updateCharts() {
  if (weeklyChart) {
    weeklyChart.destroy();
    createWeeklyChart();
  }

  if (successChart) {
    successChart.destroy();
    createSuccessChart();
  }
}

// Export functions for global access
window.ProgressApp = {
  practiceWord,
  exportProgress,
  resetProgress,
  updateCharts,
};

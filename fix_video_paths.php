<?php
// Fix video paths in database
require_once 'config.php';

echo "<h2>Fixing Video Paths</h2>";

try {
    $conn = getDBConnection();
    
    // Get all words with video paths
    $words = fetchAll("SELECT id, word, video_path FROM words WHERE video_path IS NOT NULL AND video_path != ''");
    
    echo "<p>Found " . count($words) . " words with video paths</p>";
    
    $fixed = 0;
    $errors = 0;
    
    foreach ($words as $word) {
        $oldPath = $word['video_path'];
        
        // Check if path needs fixing (contains full server path)
        if (strpos($oldPath, __DIR__) === 0) {
            // Convert to web path
            $newPath = str_replace(__DIR__ . '/', '', $oldPath);
            
            // Update database
            $stmt = $conn->prepare("UPDATE words SET video_path = ? WHERE id = ?");
            $stmt->bind_param("si", $newPath, $word['id']);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ Fixed: {$word['word']} - {$oldPath} → {$newPath}</p>";
                $fixed++;
            } else {
                echo "<p style='color: red;'>✗ Error fixing: {$word['word']}</p>";
                $errors++;
            }
            
            $stmt->close();
        } else {
            echo "<p style='color: blue;'>- OK: {$word['word']} - {$oldPath}</p>";
        }
    }
    
    $conn->close();
    
    echo "<h3>Summary</h3>";
    echo "<p>Fixed: $fixed</p>";
    echo "<p>Errors: $errors</p>";
    echo "<p>Total processed: " . count($words) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
</style>

<?php
// Reset progress API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }
    
    $conn = getDBConnection();
    
    // Start transaction
    $conn->autocommit(false);
    
    try {
        // Reset word statistics
        $stmt = $conn->prepare("UPDATE words SET times_studied = 0, times_remembered = 0, times_forgot = 0, success_rate = 0.00");
        if (!$stmt->execute()) {
            throw new Exception('هەڵەیەک ڕوویدا لە ڕیسێتکردنی ئامارەکانی وشەکان');
        }
        $stmt->close();
        
        // Delete all study sessions
        $stmt = $conn->prepare("DELETE FROM study_sessions");
        if (!$stmt->execute()) {
            throw new Exception('هەڵەیەک ڕوویدا لە سڕینەوەی مێژووی خوێندن');
        }
        $stmt->close();
        
        // Delete all daily progress
        $stmt = $conn->prepare("DELETE FROM daily_progress");
        if (!$stmt->execute()) {
            throw new Exception('هەڵەیەک ڕوویدا لە سڕینەوەی پێشکەوتنی ڕۆژانە');
        }
        $stmt->close();
        
        // Commit transaction
        $conn->commit();
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }
    
    $conn->autocommit(true);
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'هەموو ئامارەکان بە سەرکەوتوویی ڕیسێتکران'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

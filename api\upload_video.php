<?php
// Upload video API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }

    $wordId = isset($_POST['word_id']) ? (int)$_POST['word_id'] : 0;

    if ($wordId <= 0) {
        throw new Exception('ناسنامەی وشە نادروستە');
    }

    // Check if word exists
    $word = fetchRow("SELECT id FROM words WHERE id = ?", [$wordId]);
    if (!$word) {
        throw new Exception('وشە نەدۆزرایەوە');
    }

    // Check if video file is uploaded
    if (!isset($_FILES['video']) || $_FILES['video']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('فایلی ڤیدیۆ پێویستە');
    }

    $file = $_FILES['video'];

    // Validate file size
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('قەبارەی فایل زۆر گەورەیە. حەدی ئەوپەڕ: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
    }

    // Validate file type
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_VIDEO_TYPES)) {
        throw new Exception('جۆری فایل پشتگیری ناکرێت. جۆرە قبوولەکان: ' . implode(', ', ALLOWED_VIDEO_TYPES));
    }

    // Generate unique filename
    $filename = 'word_' . $wordId . '_' . time() . '.' . $fileExtension;
    $uploadPath = UPLOAD_DIR . $filename;
    $webPath = UPLOAD_URL . $filename; // Store web-accessible path

    // Create upload directory if it doesn't exist
    if (!file_exists(UPLOAD_DIR)) {
        mkdir(UPLOAD_DIR, 0777, true);
    }

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception('هەڵەیەک ڕوویدا لە بارکردنی فایل');
    }

    // Update word with video path
    $conn = getDBConnection();

    // Remove old video if exists
    $oldVideo = fetchRow("SELECT video_path FROM words WHERE id = ?", [$wordId]);
    if ($oldVideo && $oldVideo['video_path']) {
        $oldFilePath = UPLOAD_DIR . basename($oldVideo['video_path']);
        if (file_exists($oldFilePath)) {
            unlink($oldFilePath);
        }
    }

    $stmt = $conn->prepare("UPDATE words SET video_path = ? WHERE id = ?");
    $stmt->bind_param("si", $webPath, $wordId);

    if (!$stmt->execute()) {
        // Remove uploaded file if database update fails
        unlink($uploadPath);
        throw new Exception('هەڵەیەک ڕوویدا لە نوێکردنەوەی وشە');
    }

    $stmt->close();
    $conn->close();

    echo json_encode([
        'success' => true,
        'message' => 'ڤیدیۆ بە سەرکەوتوویی بارکرا',
        'video_path' => $webPath
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

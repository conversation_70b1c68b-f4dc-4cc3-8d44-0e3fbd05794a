<?php
require_once 'config.php';

// Require authentication
requireAuth();

// Get current user
$currentUser = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تاقیکردنەوەی تایبەتمەندییەکان - دەستەڕێ</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            margin-bottom: 2rem;
        }
        
        .test-button {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            margin: 0.5rem;
            transition: var(--transition);
        }
        
        .test-button:hover {
            background: var(--secondary-color);
        }
        
        .feature-status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 1rem;
        }
        
        .status-working {
            background: #d4edda;
            color: #155724;
        }
        
        .status-testing {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php 
        $pageSubtitle = "تاقیکردنەوەی تایبەتمەندییەکان";
        include 'includes/header.php'; 
        ?>
        
        <div class="test-container">
            <div class="test-section">
                <h2>🎯 <?php echo __('test_features'); ?></h2>
                <p><?php echo __('test_description'); ?></p>
                <div style="margin: 1rem 0; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                    <strong>Current Language:</strong> <?php echo getLanguageName(); ?> (<?php echo getCurrentLanguage(); ?>)<br>
                    <strong>Direction:</strong> <?php echo getLanguageDirection(); ?><br>
                    <strong>Dark Mode:</strong> <span id="darkModeStatus">Check settings</span>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🌐 زمان و ناوبری</h3>
                <span class="feature-status status-working">✅ کارا</span>
                <p>تایبەتمەندییەکانی زمان:</p>
                <ul>
                    <li>گۆڕینی زمان لە کوردی، عەرەبی، ئینگلیزی</li>
                    <li>ئاڕاستەی نووسین (RTL/LTR)</li>
                    <li>وەرگێڕانی ناوەڕۆک</li>
                </ul>
                <button class="test-button" onclick="testLanguageSwitch()">تاقیکردنەوەی گۆڕینی زمان</button>
            </div>
            
            <div class="test-section">
                <h3>⚙️ ڕێکخستنەکان</h3>
                <span class="feature-status status-working">✅ کارا</span>
                <p>تایبەتمەندییەکانی ڕێکخستن:</p>
                <ul>
                    <li>دۆخی تاریک/ڕووناک</li>
                    <li>ئاگادارکردنەوەکان</li>
                    <li>پەخشی خۆکار</li>
                    <li>بیرخستنەوەی خوێندن</li>
                </ul>
                <button class="test-button" onclick="toggleSettings()">کردنەوەی ڕێکخستنەکان</button>
            </div>
            
            <div class="test-section">
                <h3>🔔 ئاگادارکردنەوەکان</h3>
                <span class="feature-status status-working">✅ کارا</span>
                <p>سیستەمی ئاگادارکردنەوە:</p>
                <ul>
                    <li>دەستکەوتەکان</li>
                    <li>بیرخستنەوەی خوێندن</li>
                    <li>کورتەی پێشکەوتن</li>
                </ul>
                <button class="test-button" onclick="toggleNotifications()">کردنەوەی ئاگادارکردنەوەکان</button>
            </div>
            
            <div class="test-section">
                <h3>📊 هەناردەکردنی زانیاری</h3>
                <span class="feature-status status-working">✅ کارا</span>
                <p>هەناردەکردنی زانیاری:</p>
                <ul>
                    <li>فۆڕماتی JSON</li>
                    <li>فۆڕماتی CSV</li>
                    <li>ئامارەکان و پێشکەوتن</li>
                </ul>
                <button class="test-button" onclick="exportData()">تاقیکردنەوەی هەناردەکردن</button>
                <button class="test-button" onclick="testExportData()">تاقیکردنەوەی هەناردەکردنی تاقیگەیی</button>
            </div>
            
            <div class="test-section">
                <h3>🔗 بەستەرەکانی ناوبری</h3>
                <span class="feature-status status-working">✅ کارا</span>
                <p>ناوبری لە نێوان پەڕەکان:</p>
                <ul>
                    <li>پڕۆفایل</li>
                    <li>یارمەتی</li>
                    <li>پەیوەندی</li>
                    <li>سیاسەتی تایبەتێتی</li>
                </ul>
                <button class="test-button" onclick="showProfile()">پڕۆفایل</button>
                <button class="test-button" onclick="showHelp()">یارمەتی</button>
                <button class="test-button" onclick="showContact()">پەیوەندی</button>
            </div>
            
            <div class="test-section">
                <h3>📱 کارکردن لە هەموو پەڕەکان</h3>
                <span class="feature-status status-working">✅ کارا</span>
                <p>تایبەتمەندییەکان لە هەموو پەڕەکاندا کاردەکەن:</p>
                <ul>
                    <li>سەرەکی (index.php)</li>
                    <li>گەڕان (search.php)</li>
                    <li>پڕۆفایل (profile.php)</li>
                    <li>یارمەتی (help.php)</li>
                    <li>پەڕەکانی یاسایی</li>
                </ul>
                <a href="search.php" class="test-button">چوون بۆ گەڕان</a>
                <a href="index.php" class="test-button">گەڕانەوە بۆ سەرەکی</a>
                <a href="privacy.php" class="test-button">سیاسەتی تایبەتێتی</a>
                <a href="terms.php" class="test-button">مەرجەکان</a>
                <a href="contact.php" class="test-button">پەیوەندی</a>
            </div>
            
            <div class="test-section">
                <h3>🎨 دۆخی تاریک</h3>
                <span class="feature-status status-working">✅ کارا</span>
                <p>دۆخی تاریک بۆ هەموو پەڕەکان:</p>
                <button class="test-button" onclick="testDarkMode()">تاقیکردنەوەی دۆخی تاریک</button>
            </div>
        </div>
    </div>
    
    <script src="js/shared.js"></script>
    <script>
        function testLanguageSwitch() {
            showToast('تاقیکردنەوەی گۆڕینی زمان...', 'info');
            setTimeout(() => {
                toggleLanguageMenu();
            }, 1000);
        }
        
        function testDarkMode() {
            const darkModeCheckbox = document.getElementById('darkMode');
            if (darkModeCheckbox) {
                darkModeCheckbox.checked = !darkModeCheckbox.checked;
                toggleDarkMode();
            } else {
                showToast('دۆخی تاریک لە ڕێکخستنەکان چالاک بکە', 'info');
                toggleSettings();
            }
        }
        
        // Show success message on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showToast('هەموو تایبەتمەندییەکان بە سەرکەوتوویی بارکران!', 'success');
            }, 1000);
        });
    </script>
</body>
</html>

<?php
// Language Helper Functions

// Get current language from session or default
function getCurrentLanguage() {
    return $_SESSION['language'] ?? 'ckb';
}

// Set current language
function setLanguage($lang) {
    $supportedLanguages = ['ckb', 'ar', 'en'];
    if (in_array($lang, $supportedLanguages)) {
        $_SESSION['language'] = $lang;
        return true;
    }
    return false;
}

// Load language file
function loadLanguage($lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    $langFile = __DIR__ . "/../lang/{$lang}.php";
    if (file_exists($langFile)) {
        return include $langFile;
    }
    
    // Fallback to Kurdish if language file not found
    return include __DIR__ . "/../lang/ckb.php";
}

// Get translation
function __($key, ...$args) {
    static $translations = null;
    
    if ($translations === null) {
        $translations = loadLanguage();
    }
    
    $text = $translations[$key] ?? $key;
    
    // Handle sprintf formatting
    if (!empty($args)) {
        return sprintf($text, ...$args);
    }
    
    return $text;
}

// Get translation (alias)
function t($key, ...$args) {
    return __($key, ...$args);
}

// Get language direction
function getLanguageDirection($lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    $rtlLanguages = ['ckb', 'ar'];
    return in_array($lang, $rtlLanguages) ? 'rtl' : 'ltr';
}

// Get language name
function getLanguageName($lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    $names = [
        'ckb' => 'کوردی',
        'ar' => 'العربية',
        'en' => 'English'
    ];
    
    return $names[$lang] ?? $names['ckb'];
}

// Get all supported languages
function getSupportedLanguages() {
    return [
        'ckb' => [
            'name' => 'کوردی',
            'flag' => '🏴',
            'dir' => 'rtl'
        ],
        'ar' => [
            'name' => 'العربية',
            'flag' => '🇸🇦',
            'dir' => 'rtl'
        ],
        'en' => [
            'name' => 'English',
            'flag' => '🇺🇸',
            'dir' => 'ltr'
        ]
    ];
}

// Handle language change request
function handleLanguageChange() {
    if (isset($_POST['language']) || isset($_GET['lang'])) {
        $newLang = $_POST['language'] ?? $_GET['lang'];
        if (setLanguage($newLang)) {
            // Reload translations
            global $translations;
            $translations = null;
            
            // Return success response for AJAX requests
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'language' => $newLang,
                    'direction' => getLanguageDirection($newLang),
                    'name' => getLanguageName($newLang)
                ]);
                exit;
            }
        }
    }
}

// Initialize language system
function initLanguage() {
    // Handle language change requests
    handleLanguageChange();
    
    // Load current language translations
    global $translations;
    $translations = loadLanguage();
}

// Call initialization
initLanguage();
?>

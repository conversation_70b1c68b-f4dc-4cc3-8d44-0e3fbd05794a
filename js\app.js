// Main Application JavaScript
// Dastare - Kurdish Sign Language Learning App

// Global variables
let currentUser = null;
let appSettings = {
  language: "ckb",
  theme: "light",
  autoplay: true,
  studyReminders: true,
};

// Initialize app when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
});

// Initialize application
function initializeApp() {
  console.log("Initializing Dastare Sign Language App...");

  // Load user settings
  loadUserSettings();

  // Setup event listeners
  setupEventListeners();

  // Initialize tooltips and other UI components
  initializeUIComponents();

  console.log("App initialized successfully");
}

// Load user settings from localStorage
function loadUserSettings() {
  const savedSettings = localStorage.getItem("dastareSettings");
  if (savedSettings) {
    appSettings = { ...appSettings, ...JSON.parse(savedSettings) };
  }
}

// Save user settings to localStorage
function saveUserSettings() {
  localStorage.setItem("dastareSettings", JSON.stringify(appSettings));
}

// Setup global event listeners
function setupEventListeners() {
  // Handle navigation active states
  updateActiveNavigation();

  // Handle form submissions
  setupFormHandlers();

  // Handle keyboard shortcuts
  setupKeyboardShortcuts();

  // Handle responsive navigation
  setupResponsiveNavigation();
}

// Update active navigation item
function updateActiveNavigation() {
  const currentPage = window.location.pathname.split("/").pop() || "index.php";
  const navItems = document.querySelectorAll(".nav-item");

  navItems.forEach((item) => {
    const href = item.getAttribute("href");
    if (href === currentPage || (currentPage === "" && href === "index.php")) {
      item.classList.add("active");
    } else {
      item.classList.remove("active");
    }
  });
}

// Setup form handlers
function setupFormHandlers() {
  // Handle all forms with AJAX
  const forms = document.querySelectorAll('form[data-ajax="true"]');
  forms.forEach((form) => {
    form.addEventListener("submit", handleAjaxForm);
  });
}

// Handle AJAX form submissions
async function handleAjaxForm(event) {
  event.preventDefault();

  const form = event.target;
  const formData = new FormData(form);
  const action = form.getAttribute("action") || window.location.href;
  const method = form.getAttribute("method") || "POST";

  try {
    showLoading(true);

    const response = await fetch(action, {
      method: method,
      body: formData,
    });

    const result = await response.json();

    if (result.success) {
      showNotification(
        result.message || "عملیات بە سەرکەوتوویی تەواو بوو",
        "success"
      );

      // Handle specific actions
      if (result.action) {
        handleFormAction(result.action, result.data);
      }
    } else {
      showNotification(result.message || "هەڵەیەک ڕوویدا", "error");
    }
  } catch (error) {
    console.error("Form submission error:", error);
    showNotification("هەڵەیەک ڕوویدا لە پەیوەندی کردن", "error");
  } finally {
    showLoading(false);
  }
}

// Handle form actions after successful submission
function handleFormAction(action, data) {
  switch (action) {
    case "reload":
      window.location.reload();
      break;
    case "redirect":
      window.location.href = data.url;
      break;
    case "update_stats":
      updatePageStats(data);
      break;
    case "close_modal":
      closeAllModals();
      break;
  }
}

// Setup keyboard shortcuts
function setupKeyboardShortcuts() {
  document.addEventListener("keydown", function (event) {
    // Escape key to close modals
    if (event.key === "Escape") {
      closeAllModals();
    }

    // Ctrl/Cmd + K for search
    if ((event.ctrlKey || event.metaKey) && event.key === "k") {
      event.preventDefault();
      focusSearchInput();
    }

    // Arrow keys for navigation in study mode
    if (
      document.querySelector('.study-session:not([style*="display: none"])')
    ) {
      handleStudyKeyboard(event);
    }
  });
}

// Setup responsive navigation
function setupResponsiveNavigation() {
  // Handle mobile navigation scrolling
  const navContainer = document.querySelector(".nav-container");
  if (navContainer) {
    // Ensure active item is visible on mobile
    const activeItem = navContainer.querySelector(".nav-item.active");
    if (activeItem) {
      activeItem.scrollIntoView({ behavior: "smooth", inline: "center" });
    }
  }
}

// Initialize UI components
function initializeUIComponents() {
  // Initialize tooltips
  initializeTooltips();

  // Initialize animations
  initializeAnimations();

  // Initialize lazy loading for videos
  initializeLazyLoading();

  // Ensure all videos are muted
  muteAllVideos();

  // Start continuous monitoring
  startVideoMuteMonitoring();
}

// Initialize tooltips
function initializeTooltips() {
  const tooltipElements = document.querySelectorAll("[data-tooltip]");
  tooltipElements.forEach((element) => {
    element.addEventListener("mouseenter", showTooltip);
    element.addEventListener("mouseleave", hideTooltip);
  });
}

// Initialize animations
function initializeAnimations() {
  // Fade in animations for cards
  const cards = document.querySelectorAll(
    ".stat-card, .action-card, .word-card, .mode-card"
  );
  cards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.1}s`;
    card.classList.add("fade-in");
  });
}

// Initialize lazy loading for videos
function initializeLazyLoading() {
  const videos = document.querySelectorAll("video[data-src]");

  if ("IntersectionObserver" in window) {
    const videoObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const video = entry.target;
          video.src = video.dataset.src;
          video.muted = true; // Ensure lazy-loaded videos are muted
          video.volume = 0; // Set volume to 0
          video.classList.add("force-muted"); // Add force-muted class
          setupVideoProtection(video); // Setup protection
          video.removeAttribute("data-src");
          videoObserver.unobserve(video);
        }
      });
    });

    videos.forEach((video) => videoObserver.observe(video));
  }
}

// Ensure all videos are muted and prevent unmuting
function muteAllVideos() {
  const videos = document.querySelectorAll("video");
  videos.forEach((video) => {
    // Force mute and volume to 0
    video.muted = true;
    video.volume = 0;

    // Add class for CSS styling to hide volume controls
    video.classList.add("force-muted");

    // Setup protection for this video
    setupVideoProtection(video);
  });
}

// Start continuous monitoring to ensure videos stay muted
function startVideoMuteMonitoring() {
  // Monitor every 500ms to ensure videos stay muted
  setInterval(() => {
    const videos = document.querySelectorAll("video");
    videos.forEach((video) => {
      if (!video.muted || video.volume > 0) {
        video.muted = true;
        video.volume = 0;
        video.classList.add("force-muted");
      }
    });
  }, 500);

  // Also monitor when new videos are added to the DOM
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === 1) {
          // Element node
          // Check if the added node is a video
          if (node.tagName === "VIDEO") {
            node.muted = true;
            node.volume = 0;
            node.classList.add("force-muted");
            setupVideoProtection(node);
          }
          // Check for videos within the added node
          const videos =
            node.querySelectorAll && node.querySelectorAll("video");
          if (videos) {
            videos.forEach((video) => {
              video.muted = true;
              video.volume = 0;
              video.classList.add("force-muted");
              setupVideoProtection(video);
            });
          }
        }
      });
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
}

// Setup protection for individual video element
function setupVideoProtection(video) {
  // Listen for any attempts to change volume or muted state
  video.addEventListener("volumechange", function (e) {
    if (!this.muted || this.volume > 0) {
      this.muted = true;
      this.volume = 0;
    }
  });

  // Prevent context menu to avoid unmuting through right-click
  video.addEventListener("contextmenu", function (e) {
    e.preventDefault();
  });

  // Monitor for attribute changes
  const observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
      if (
        mutation.type === "attributes" &&
        mutation.attributeName === "muted"
      ) {
        if (!video.muted) {
          video.muted = true;
          video.volume = 0;
        }
      }
    });
  });

  observer.observe(video, {
    attributes: true,
    attributeFilter: ["muted"],
  });
}

// Utility Functions

// Show loading indicator
function showLoading(show = true) {
  let loader = document.getElementById("globalLoader");

  if (show && !loader) {
    loader = document.createElement("div");
    loader.id = "globalLoader";
    loader.className = "global-loader";
    loader.innerHTML = `
            <div class="loader-content">
                <div class="spinner"></div>
                <p>چاوەڕێ بکە...</p>
            </div>
        `;
    document.body.appendChild(loader);
  }

  if (loader) {
    loader.style.display = show ? "flex" : "none";
    if (!show) {
      setTimeout(() => loader.remove(), 300);
    }
  }
}

// Show notification
function showNotification(message, type = "info", duration = 5000) {
  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;

  const icon = getNotificationIcon(type);
  notification.innerHTML = `
        <div class="notification-content">
            <i class="${icon}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

  // Add to container or create one
  let container = document.getElementById("notificationContainer");
  if (!container) {
    container = document.createElement("div");
    container.id = "notificationContainer";
    container.className = "notification-container";
    document.body.appendChild(container);
  }

  container.appendChild(notification);

  // Auto remove after duration
  setTimeout(() => {
    notification.classList.add("fade-out");
    setTimeout(() => notification.remove(), 300);
  }, duration);
}

// Get notification icon based on type
function getNotificationIcon(type) {
  const icons = {
    success: "fas fa-check-circle",
    error: "fas fa-exclamation-circle",
    warning: "fas fa-exclamation-triangle",
    info: "fas fa-info-circle",
  };
  return icons[type] || icons.info;
}

// Close all modals
function closeAllModals() {
  const modals = document.querySelectorAll(".modal");
  modals.forEach((modal) => {
    modal.classList.remove("active");
    modal.style.display = "none";
  });

  // Always restore body scroll
  document.body.style.overflow = "";

  // Perform comprehensive cleanup for all modals
  performModalCleanup();
}

// Perform comprehensive modal cleanup
function performModalCleanup() {
  // Reset global variables that might be set by modals
  if (typeof currentWordId !== "undefined") {
    currentWordId = null;
  }

  // Clean up video streams
  if (typeof currentStream !== "undefined" && currentStream) {
    currentStream.getTracks().forEach((track) => track.stop());
    currentStream = null;
  }

  // Pause and reset all modal videos
  const modalVideos = document.querySelectorAll(".modal video");
  modalVideos.forEach((video) => {
    video.pause();
    video.currentTime = 0;

    // Clean up blob URLs
    if (video.src && video.src.startsWith("blob:")) {
      URL.revokeObjectURL(video.src);
      video.src = "";
    }

    // Remove any recorded blob references
    if (video.recordedBlob) {
      video.recordedBlob = null;
    }
  });

  // Reset all modal forms
  const modalForms = document.querySelectorAll(".modal form");
  modalForms.forEach((form) => {
    form.reset();
  });

  // Hide all video upload sections and recording interfaces
  const videoSections = document.querySelectorAll(
    ".video-upload-section, .video-recording, .video-selection, .video-cropper"
  );
  videoSections.forEach((section) => {
    section.style.display = "none";
  });

  // Reset video option buttons
  const videoOptionBtns = document.querySelectorAll(".video-option-btn");
  videoOptionBtns.forEach((btn) => {
    btn.classList.remove("active");
  });

  // Reset any file inputs
  const fileInputs = document.querySelectorAll(".modal input[type='file']");
  fileInputs.forEach((input) => {
    input.value = "";
  });

  // Clean up any loading indicators
  const loadingIndicators = document.querySelectorAll(
    ".modal .loading-spinner"
  );
  loadingIndicators.forEach((indicator) => {
    indicator.remove();
  });
}

// Focus search input
function focusSearchInput() {
  const searchInput = document.querySelector('.search-input, input[name="q"]');
  if (searchInput) {
    searchInput.focus();
    searchInput.select();
  }
}

// Update page statistics
function updatePageStats(data) {
  // Update stat cards
  Object.keys(data).forEach((key) => {
    const element = document.querySelector(`[data-stat="${key}"]`);
    if (element) {
      element.textContent = data[key];
    }
  });
}

// Format numbers for display
function formatNumber(number) {
  return new Intl.NumberFormat("en-US").format(number);
}

// Format time duration
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  }
  return `${minutes}:${secs.toString().padStart(2, "0")}`;
}

// Show tooltip
function showTooltip(event) {
  const element = event.target;
  const text = element.getAttribute("data-tooltip");

  const tooltip = document.createElement("div");
  tooltip.className = "tooltip";
  tooltip.textContent = text;

  document.body.appendChild(tooltip);

  const rect = element.getBoundingClientRect();
  tooltip.style.left =
    rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + "px";
  tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + "px";

  element._tooltip = tooltip;
}

// Hide tooltip
function hideTooltip(event) {
  const element = event.target;
  if (element._tooltip) {
    element._tooltip.remove();
    delete element._tooltip;
  }
}

// Debounce function for search
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Export functions for use in other files
window.DastareApp = {
  showLoading,
  showNotification,
  closeAllModals,
  formatNumber,
  formatDuration,
  debounce,
  muteAllVideos,
  startVideoMuteMonitoring,
  setupVideoProtection,
  performModalCleanup,
};

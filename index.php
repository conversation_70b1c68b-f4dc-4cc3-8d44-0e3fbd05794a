<?php
require_once 'config.php';

// Require authentication
requireAuth();

// Get current user
$currentUser = getCurrentUser();
$userId = getUserId();

// Get today's progress for current user
$today = date('Y-m-d');
$todayProgress = fetchRow("SELECT * FROM daily_progress WHERE user_id = ? AND study_date = ?", [$userId, $today]);

if (!$todayProgress) {
    // Create today's progress entry for current user
    executeQuery("INSERT INTO daily_progress (user_id, study_date) VALUES (?, ?)", [$userId, $today]);
    $todayProgress = ['words_studied' => 0, 'words_remembered' => 0, 'words_forgot' => 0, 'success_rate' => 0, 'study_time_minutes' => 0];
}

// Get total words count
$totalWords = getCount("SELECT COUNT(*) FROM words");

// Get recent progress (last 7 days) for current user
$recentProgress = fetchAll("SELECT * FROM daily_progress WHERE user_id = ? AND study_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) ORDER BY study_date DESC", [$userId]);

// Check if this is a new user (welcome message)
$isNewUser = isset($_GET['welcome']) && $_GET['welcome'] == '1';
$showWelcome = $isNewUser || (empty($recentProgress) && $todayProgress['words_studied'] == 0);
?>

<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دەستەڕێ - فێربوونی زمانی ئاماژە</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="فێربوونی زمانی ئاماژەی کوردی بە شێوەی مۆدێرن">
    <meta name="theme-color" content="#4f46e5">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دەستەڕێ">
    <meta name="msapplication-TileColor" content="#4f46e5">
    <meta name="msapplication-config" content="browserconfig.xml">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/icons/72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/icons/72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/192.png">
    <link rel="mask-icon" href="assets/icons/192.png" color="#4f46e5">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <?php include 'includes/header.php'; ?>

        <!-- Navigation -->
        <nav class="nav">
            <div class="nav-container">
                <a href="index.php" class="nav-item active">
                    <i class="fas fa-home"></i>
                    <span><?php echo __('nav_home'); ?></span>
                </a>
                <a href="search.php" class="nav-item">
                    <i class="fas fa-search"></i>
                    <span><?php echo __('nav_search'); ?></span>
                </a>
                <a href="study.php" class="nav-item">
                    <i class="fas fa-graduation-cap"></i>
                    <span><?php echo __('nav_study'); ?></span>
                </a>
                <a href="progress.php" class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span><?php echo __('nav_progress'); ?></span>
                </a>
            </div>
        </nav>

        <!-- Welcome Message for New Users -->
        <?php if ($showWelcome): ?>
        <div class="welcome-banner">
            <div class="welcome-content">
                <h2><i class="fas fa-hands"></i> <?php echo __('welcome'); ?> <?php echo htmlspecialchars($currentUser['name']); ?>!</h2>
                <p><?php echo __('app_subtitle'); ?>. <?php echo __('start_learning'); ?>.</p>
                <div class="welcome-actions">
                    <a href="search.php" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        <?php echo __('start_learning'); ?>
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Main Content -->
        <main class="main">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($totalWords); ?></h3>
                        <p><?php echo __('total_words'); ?></p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $todayProgress['words_studied']; ?></h3>
                        <p><?php echo __('words_studied'); ?> <?php echo __('today'); ?></p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $todayProgress['words_remembered']; ?></h3>
                        <p><?php echo __('remembered'); ?></p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($todayProgress['success_rate'], 1); ?>%</h3>
                        <p><?php echo __('success_rate'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2><?php echo __('quick_actions'); ?></h2>
                <div class="actions-grid">
                    <a href="study.php" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-play"></i>
                        </div>
                        <h3><?php echo __('start_study_session'); ?></h3>
                        <p><?php echo __('start_learning'); ?></p>
                    </a>

                    <a href="search.php" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3><?php echo __('browse_words'); ?></h3>
                        <p><?php echo __('search_words'); ?></p>
                    </a>

                    <a href="search.php#add-word" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <h3><?php echo __('add_new_word'); ?></h3>
                        <p><?php echo __('add_new_word'); ?></p>
                    </a>

                    <a href="progress.php" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3><?php echo __('view_progress'); ?></h3>
                        <p><?php echo __('your_progress'); ?></p>
                    </a>
                </div>
            </div>

            <!-- Recent Progress -->
            <div class="recent-progress">
                <h2><?php echo __('recent_progress'); ?></h2>
                <div class="progress-list">
                    <?php foreach ($recentProgress as $progress): ?>
                        <div class="progress-item">
                            <div class="progress-date">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('Y/m/d', strtotime($progress['study_date'])); ?>
                            </div>
                            <div class="progress-stats">
                                <span class="stat">
                                    <i class="fas fa-book"></i>
                                    <?php echo $progress['words_studied']; ?> <?php echo __('words_studied'); ?>
                                </span>
                                <span class="stat">
                                    <i class="fas fa-check"></i>
                                    <?php echo $progress['words_remembered']; ?> <?php echo __('remembered'); ?>
                                </span>
                                <span class="stat">
                                    <i class="fas fa-percentage"></i>
                                    <?php echo number_format($progress['success_rate'], 1); ?>%
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>



        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; ٢٠٢٥ <?php echo __('app_title'); ?> - <?php echo __('app_subtitle'); ?></p>
                <div class="footer-links">
                    <a href="#" onclick="showPrivacyPolicy()"><?php echo __('privacy_policy'); ?></a>
                    <a href="#" onclick="showTerms()"><?php echo __('terms_of_service'); ?></a>
                    <a href="#" onclick="showContact()"><?php echo __('contact_us'); ?></a>
                </div>
            </div>
        </footer>
    </div>

    <script src="js/app.js"></script>
    <script src="js/shared.js"></script>

    <!-- PWA Install Prompt -->
    <div id="pwa-install-banner" class="pwa-banner" style="display: none;">
        <div class="pwa-banner-content">
            <img src="assets/icons/72.png" alt="دەستەڕێ" class="pwa-icon">
            <div class="pwa-text">
                <h4>دابەزاندنی ئەپ</h4>
                <p>دەستەڕێ دابەزێنە بۆ دەستگەیشتنی خێراتر</p>
            </div>
            <button id="pwa-install-btn" class="pwa-install-btn">دابەزاندن</button>
            <button id="pwa-dismiss-btn" class="pwa-dismiss-btn">×</button>
        </div>
    </div>

    <!-- PWA Service Worker Registration -->
    <script>
        // Register service worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        const installBanner = document.getElementById('pwa-install-banner');
        const installBtn = document.getElementById('pwa-install-btn');
        const dismissBtn = document.getElementById('pwa-dismiss-btn');

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install banner if not dismissed before
            if (!localStorage.getItem('pwa-dismissed')) {
                installBanner.style.display = 'block';
            }
        });

        installBtn.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                deferredPrompt = null;
                installBanner.style.display = 'none';
            }
        });

        dismissBtn.addEventListener('click', () => {
            installBanner.style.display = 'none';
            localStorage.setItem('pwa-dismissed', 'true');
        });

        // Hide banner when app is installed
        window.addEventListener('appinstalled', () => {
            installBanner.style.display = 'none';
            console.log('PWA was installed');
        });
    </script>
</body>
</html>

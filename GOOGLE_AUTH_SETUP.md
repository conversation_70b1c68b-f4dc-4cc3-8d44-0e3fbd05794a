# Google Authentication Setup for Dastare

This guide will help you set up Google OAuth authentication for the Dastare sign language learning application.

## Prerequisites

- XAMPP or similar local server environment
- Google account
- Access to Google Cloud Console

## Step 1: Google Cloud Console Setup

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/

2. **Create or Select a Project**
   - Create a new project or select an existing one
   - Note the project name/ID

3. **Enable Required APIs**
   - Go to "APIs & Services" > "Library"
   - Search for and enable: "Google+ API" or "Google People API"

4. **Create OAuth 2.0 Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add these to "Authorized redirect URIs":
     ```
     http://localhost/dastare/auth/google_callback.php
     http://yourdomain.com/dastare/auth/google_callback.php
     ```
   - Replace `yourdomain.com` with your actual domain if deploying online

5. **Get Your Credentials**
   - Copy the Client ID and Client Secret

## Step 2: Configure the Application

1. **Update auth_config.php**
   - Open `auth_config.php`
   - Replace the placeholder values:
   ```php
   define('GOOGLE_CLIENT_ID', 'your_actual_client_id_here');
   define('GOOGLE_CLIENT_SECRET', 'your_actual_client_secret_here');
   ```

2. **Update Redirect URI (if needed)**
   - If your application is not at `http://localhost/dastare/`, update:
   ```php
   define('GOOGLE_REDIRECT_URI', 'http://your-domain/path/auth/google_callback.php');
   ```

## Step 3: Database Setup

1. **Run the Setup Script**
   - Visit: `http://localhost/dastare/setup_auth.php`
   - Follow the on-screen instructions
   - Click "Update Database" to create the necessary tables
   - Click "Migrate Existing Data" if you have existing study data

2. **Manual Database Update (Alternative)**
   - Import the updated `dastare.sql` file
   - Or run these SQL commands:
   ```sql
   -- Create users table
   CREATE TABLE `users` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `google_id` varchar(255) NOT NULL,
     `email` varchar(255) NOT NULL,
     `name` varchar(255) NOT NULL,
     `picture` varchar(500) DEFAULT NULL,
     `locale` varchar(10) DEFAULT 'en',
     `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
     `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
     `last_login` timestamp NULL DEFAULT NULL,
     PRIMARY KEY (`id`),
     UNIQUE KEY `unique_google_id` (`google_id`),
     UNIQUE KEY `unique_email` (`email`)
   );

   -- Add user_id to existing tables
   ALTER TABLE daily_progress ADD COLUMN user_id int(11) NOT NULL AFTER id;
   ALTER TABLE study_sessions ADD COLUMN user_id int(11) NOT NULL AFTER id;

   -- Add foreign key constraints
   ALTER TABLE daily_progress ADD CONSTRAINT daily_progress_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
   ALTER TABLE study_sessions ADD CONSTRAINT study_sessions_ibfk_2 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE;
   ```

## Step 4: Test the Authentication

1. **Visit the Application**
   - Go to: `http://localhost/dastare/`
   - You should be redirected to the login page

2. **Test Google Login**
   - Click "Login with Google"
   - Complete the OAuth flow
   - You should be redirected back to the application

## Features Added

### User Management
- Google OAuth 2.0 authentication
- User registration and login
- Session management
- User profile information

### Data Isolation
- Each user has their own study progress
- Personal statistics and achievements
- Secure data access (users can only see their own data)

### User Interface
- User avatar and name display
- Logout functionality
- Welcome messages for new users

### Database Changes
- `users` table for user information
- `user_id` foreign keys in existing tables
- Proper data relationships and constraints

## Security Features

- OAuth 2.0 state parameter validation
- Session timeout management
- SQL injection protection
- User data isolation
- Secure logout functionality

## Troubleshooting

### Common Issues

1. **"OAuth error: access_denied"**
   - User cancelled the login process
   - Try again

2. **"Invalid redirect URI"**
   - Check that your redirect URI in Google Console matches exactly
   - Ensure no trailing slashes or extra characters

3. **"Invalid client ID"**
   - Verify the Client ID in `auth_config.php`
   - Make sure you copied it correctly from Google Console

4. **Database errors**
   - Run the setup script: `setup_auth.php`
   - Check database connection in `config.php`
   - Ensure proper permissions

### Debug Mode

To enable debug mode, add this to `auth_config.php`:
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## Production Deployment

When deploying to production:

1. **Update redirect URIs** in Google Console
2. **Change the redirect URI** in `auth_config.php`
3. **Use HTTPS** for security
4. **Disable debug mode**
5. **Set proper file permissions**

## Support

If you encounter issues:
1. Check the browser console for JavaScript errors
2. Check PHP error logs
3. Verify Google Console configuration
4. Test with a fresh browser session

The authentication system is now ready for use with study progress tracking and user statistics!

// Study Page JavaScript
// Handle flash card study sessions

let studySession = {
  words: [],
  currentIndex: 0,
  mode: null,
  startTime: null,
  hintUsed: false,
  stats: {
    remembered: 0,
    forgot: 0,
    total: 0,
    hintsUsed: 0,
  },
};

// Initialize study page
document.addEventListener("DOMContentLoaded", function () {
  initializeStudyPage();
});

function initializeStudyPage() {
  setupStudyHandlers();
  resetStudySession();
}

// Setup study event handlers
function setupStudyHandlers() {
  // Keyboard shortcuts for study
  document.addEventListener("keydown", handleStudyKeyboard);
}

// Handle keyboard shortcuts during study
function handleStudyKeyboard(event) {
  const studySessionElement = document.getElementById("studySession");
  if (!studySessionElement || studySessionElement.style.display === "none") {
    return;
  }

  switch (event.key) {
    case "ArrowLeft":
    case "1":
      event.preventDefault();
      markWord("forgot");
      break;
    case "ArrowRight":
    case "2":
      event.preventDefault();
      markWord("remembered");
      break;
    case " ":
      event.preventDefault();
      showHint();
      break;
    case "Escape":
      event.preventDefault();
      if (confirm("دڵنیای لە کۆتایی هێنان بە خوێندنەکە؟")) {
        endStudySession();
      }
      break;
  }
}

// Start study mode
async function startStudyMode(mode) {
  try {
    DastareApp.showLoading(true);

    const response = await fetch(
      `api/get_study_words.php?mode=${mode}&limit=10`
    );
    const data = await response.json();

    if (data.success && data.words.length > 0) {
      studySession.words = data.words;
      studySession.mode = mode;
      studySession.currentIndex = 0;
      studySession.startTime = Date.now();
      studySession.stats = {
        remembered: 0,
        forgot: 0,
        total: data.words.length,
        hintsUsed: 0,
      };

      showStudySession();
      loadCurrentWord();
    } else {
      DastareApp.showNotification(
        data.message || "هیچ وشەیەک بۆ خوێندن نەدۆزرایەوە",
        "warning"
      );
    }
  } catch (error) {
    console.error("Error starting study session:", error);
    DastareApp.showNotification(
      "هەڵەیەک ڕوویدا لە دەستپێکردنی خوێندن",
      "error"
    );
  } finally {
    DastareApp.showLoading(false);
  }
}

// Show study session interface
function showStudySession() {
  document.getElementById("studyModeSelection").style.display = "none";
  document.getElementById("studySession").style.display = "block";
  document.getElementById("sessionComplete").style.display = "none";

  // Update session info
  document.getElementById("totalWordsInSession").textContent =
    studySession.stats.total;
  updateSessionProgress();
}

// Load current word
function loadCurrentWord() {
  const word = studySession.words[studySession.currentIndex];
  if (!word) {
    completeStudySession();
    return;
  }

  // Update word display
  document.getElementById("currentWord").textContent = word.word;
  document.getElementById("currentWordNumber").textContent =
    studySession.currentIndex + 1;

  // Handle video and hint system
  const video = document.getElementById("studyVideo");
  const noVideoPlaceholder = document.getElementById("noVideoPlaceholder");
  const hintBtn = document.getElementById("hintBtn");

  // Reset hint state for new word
  studySession.hintUsed = false;

  if (word.video_path) {
    video.src = word.video_path;
    video.muted = true; // Ensure video is muted
    video.volume = 0; // Set volume to 0
    video.classList.add("force-muted"); // Add force-muted class
    video.style.display = "none"; // Always hide initially
    noVideoPlaceholder.style.display = "block"; // Show placeholder initially
    hintBtn.style.display = "inline-flex"; // Show hint button
    hintBtn.classList.remove("used");
    hintBtn.innerHTML = '<i class="fas fa-lightbulb"></i><span>ئاماژە</span>';
  } else {
    video.style.display = "none";
    noVideoPlaceholder.style.display = "block";
    hintBtn.style.display = "none"; // No hint if no video
  }

  // Update word info
  const descriptionElement = document.getElementById("wordDescription");
  const difficultyElement = document.getElementById("wordDifficulty");

  if (word.description) {
    descriptionElement.textContent = word.description;
    descriptionElement.style.display = "block";
  } else {
    descriptionElement.style.display = "none";
  }

  difficultyElement.textContent = `ئاستی سەختی: ${getDifficultyText(
    word.difficulty_level
  )}`;

  updateSessionProgress();
}

// Get difficulty text in Kurdish
function getDifficultyText(level) {
  const levels = {
    easy: "ئاسان",
    medium: "مامناوەند",
    hard: "سەخت",
  };
  return levels[level] || "مامناوەند";
}

// Show hint (video) when user needs help
function showHint() {
  if (studySession.hintUsed) {
    return; // Hint already used for this word
  }

  const video = document.getElementById("studyVideo");
  const noVideoPlaceholder = document.getElementById("noVideoPlaceholder");
  const hintBtn = document.getElementById("hintBtn");

  // Show the video
  video.style.display = "block";
  noVideoPlaceholder.style.display = "none";
  video.play();

  // Mark hint as used
  studySession.hintUsed = true;
  studySession.stats.hintsUsed++;

  // Update hint button to show it's been used
  hintBtn.classList.add("used");
  hintBtn.innerHTML =
    '<i class="fas fa-check"></i><span>ئاماژە بەکارهێنرا</span>';
  hintBtn.disabled = true;

  // Show notification
  DastareApp.showNotification(
    "ئاماژە نیشاندرا! ئێستا ڤیدیۆکە ببینە",
    "info",
    3000
  );
}

// Mark word as remembered or forgot
async function markWord(result) {
  const word = studySession.words[studySession.currentIndex];
  if (!word) return;

  // Update local stats
  studySession.stats[result]++;

  // Save to database
  try {
    await fetch("api/record_study.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        word_id: word.id,
        result: result,
        response_time: Math.floor((Date.now() - studySession.startTime) / 1000),
      }),
    });
  } catch (error) {
    console.error("Error recording study result:", error);
  }

  // Update session stats display
  updateSessionStats();

  // Move to next word
  studySession.currentIndex++;

  if (studySession.currentIndex < studySession.words.length) {
    loadCurrentWord();
  } else {
    completeStudySession();
  }
}

// Update session progress
function updateSessionProgress() {
  const progress = (studySession.currentIndex / studySession.stats.total) * 100;
  document.getElementById("progressFill").style.width = `${progress}%`;
}

// Update session stats display
function updateSessionStats() {
  document.getElementById("sessionRemembered").textContent =
    studySession.stats.remembered;
  document.getElementById("sessionForgot").textContent =
    studySession.stats.forgot;

  const total = studySession.stats.remembered + studySession.stats.forgot;
  const successRate =
    total > 0 ? Math.round((studySession.stats.remembered / total) * 100) : 0;
  document.getElementById("sessionSuccessRate").textContent = `${successRate}%`;
}

// Complete study session
function completeStudySession() {
  const timeSpent = Math.floor((Date.now() - studySession.startTime) / 60000); // minutes

  // Update final stats
  document.getElementById("finalWordsStudied").textContent =
    studySession.stats.remembered + studySession.stats.forgot;
  document.getElementById("finalRemembered").textContent =
    studySession.stats.remembered;
  document.getElementById("finalHintsUsed").textContent =
    studySession.stats.hintsUsed;
  document.getElementById("finalTimeSpent").textContent = timeSpent;

  const total = studySession.stats.remembered + studySession.stats.forgot;
  const successRate =
    total > 0 ? Math.round((studySession.stats.remembered / total) * 100) : 0;
  document.getElementById("finalSuccessRate").textContent = `${successRate}%`;

  // Show completion screen
  document.getElementById("studySession").style.display = "none";
  document.getElementById("sessionComplete").style.display = "block";

  // Update daily progress
  updateDailyProgress(
    studySession.stats.remembered + studySession.stats.forgot,
    studySession.stats.remembered,
    studySession.stats.forgot,
    timeSpent
  );
}

// End study session early
function endStudySession() {
  if (studySession.stats.remembered + studySession.stats.forgot > 0) {
    completeStudySession();
  } else {
    resetStudySession();
  }
}

// Reset study session
function resetStudySession() {
  studySession = {
    words: [],
    currentIndex: 0,
    mode: null,
    startTime: null,
    hintUsed: false,
    stats: { remembered: 0, forgot: 0, total: 0, hintsUsed: 0 },
  };

  document.getElementById("studyModeSelection").style.display = "block";
  document.getElementById("studySession").style.display = "none";
  document.getElementById("sessionComplete").style.display = "none";
}

// Start new session
function startNewSession() {
  resetStudySession();
}

// Go to progress page
function goToProgress() {
  window.location.href = "progress.php";
}

// Go to home page
function goHome() {
  window.location.href = "index.php";
}

// Update daily progress in database
async function updateDailyProgress(
  wordsStudied,
  remembered,
  forgot,
  timeSpent
) {
  try {
    await fetch("api/update_progress.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        words_studied: wordsStudied,
        words_remembered: remembered,
        words_forgot: forgot,
        study_time_minutes: timeSpent,
      }),
    });
  } catch (error) {
    console.error("Error updating daily progress:", error);
  }
}

// Export functions for global access
window.StudyApp = {
  startStudyMode,
  markWord,
  showHint,
  endStudySession,
  startNewSession,
  goToProgress,
  goHome,
};

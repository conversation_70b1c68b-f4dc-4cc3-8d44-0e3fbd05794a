<?php
require_once 'config.php';

// Require authentication
requireAuth();

// Get current user
$currentUser = getCurrentUser();
$userId = getUserId();

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $locale = $_POST['locale'] ?? 'ckb';

    if (!empty($name)) {
        $conn = getDBConnection();
        $stmt = $conn->prepare("UPDATE users SET name = ?, locale = ? WHERE id = ?");
        $stmt->bind_param("ssi", $name, $locale, $userId);

        if ($stmt->execute()) {
            $_SESSION['user_name'] = $name;
            $success = "پڕۆفایل بە سەرکەوتوویی نوێکرایەوە";
        } else {
            $error = "هەڵەیەک ڕوویدا لە نوێکردنەوەی پڕۆفایل";
        }
        $stmt->close();
        $conn->close();
    }
}

// Get user statistics
try {
    $totalWordsStudied = getCount("SELECT COUNT(DISTINCT word_id) FROM study_sessions WHERE user_id = ?", [$userId]);
    $totalSessions = getCount("SELECT COUNT(*) FROM study_sessions WHERE user_id = ?", [$userId]);
    $totalDays = getCount("SELECT COUNT(*) FROM daily_progress WHERE user_id = ? AND words_studied > 0", [$userId]);
    $averageSuccess = fetchRow("SELECT AVG(success_rate) as avg_rate FROM daily_progress WHERE user_id = ? AND words_studied > 0", [$userId]);
    $longestStreak = calculateLongestStreak($userId);
    $currentStreak = calculateCurrentStreak($userId);
} catch (Exception $e) {
    // Set default values if there's an error
    $totalWordsStudied = 0;
    $totalSessions = 0;
    $totalDays = 0;
    $averageSuccess = ['avg_rate' => 0];
    $longestStreak = 0;
    $currentStreak = 0;
    $error = "هەڵەیەک ڕوویدا لە بارکردنی ئامارەکان: " . $e->getMessage();
}

function calculateLongestStreak($userId) {
    $sessions = fetchAll("SELECT DISTINCT session_date FROM study_sessions WHERE user_id = ? ORDER BY session_date", [$userId]);
    
    $longestStreak = 0;
    $currentStreak = 0;
    $lastDate = null;
    
    foreach ($sessions as $session) {
        $currentDate = new DateTime($session['session_date']);
        
        if ($lastDate === null || $currentDate->diff($lastDate)->days === 1) {
            $currentStreak++;
            $longestStreak = max($longestStreak, $currentStreak);
        } else if ($currentDate->diff($lastDate)->days > 1) {
            $currentStreak = 1;
        }
        
        $lastDate = $currentDate;
    }
    
    return $longestStreak;
}

function calculateCurrentStreak($userId) {
    $today = new DateTime();
    $sessions = fetchAll("SELECT DISTINCT session_date FROM study_sessions WHERE user_id = ? ORDER BY session_date DESC", [$userId]);
    
    $streak = 0;
    $expectedDate = $today;
    
    foreach ($sessions as $session) {
        $sessionDate = new DateTime($session['session_date']);
        $daysDiff = $expectedDate->diff($sessionDate)->days;
        
        if ($daysDiff === 0 || ($daysDiff === 1 && $streak === 0)) {
            $streak++;
            $expectedDate = $sessionDate->sub(new DateInterval('P1D'));
        } else {
            break;
        }
    }
    
    return $streak;
}
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getLanguageDirection(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('profile'); ?> - <?php echo __('app_title'); ?></title>
    <script>
        // Set initial dark mode before page renders
        (function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                if (document.documentElement) {
                    document.documentElement.classList.add('dark-mode');
                }
                if (document.body) {
                    document.body.classList.add('dark-mode');
                } else {
                    document.addEventListener('DOMContentLoaded', function() {
                        document.body.classList.add('dark-mode');
                    });
                }
            }
        })();
    </script>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="js/shared.js"></script>
    <style>
        .profile-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-box {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-md);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .profile-form {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
        }
        
        .btn-save {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .btn-save:hover {
            background: var(--secondary-color);
        }
        
        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php
        $pageSubtitle = __('profile_title');
        include 'includes/header.php';
        ?>

        <div class="profile-container">
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h1><?php echo htmlspecialchars($currentUser['name']); ?></h1>
                <p><?php echo htmlspecialchars($currentUser['email']); ?></p>
                <p><?php echo __('member_since'); ?> <?php echo date('Y/m/d', strtotime($currentUser['created_at'])); ?></p>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <div class="stats-grid">
                <div class="stat-box">
                    <div class="stat-number"><?php echo $totalWordsStudied; ?></div>
                    <div><?php echo __('words_studied'); ?></div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $totalSessions; ?></div>
                    <div><?php echo __('study_sessions'); ?></div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $totalDays; ?></div>
                    <div><?php echo __('days'); ?> <?php echo __('study'); ?></div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo number_format($averageSuccess['avg_rate'] ?? 0, 1); ?>%</div>
                    <div><?php echo __('success_rate'); ?></div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $longestStreak; ?></div>
                    <div><?php echo __('longest_streak'); ?></div>
                </div>
                <div class="stat-box">
                    <div class="stat-number"><?php echo $currentStreak; ?></div>
                    <div><?php echo __('current_streak'); ?></div>
                </div>
            </div>
            
            <div class="profile-form">
                <h2><i class="fas fa-edit"></i> <?php echo __('edit_profile'); ?></h2>

                <form method="POST">
                    <div class="form-group">
                        <label for="name"><?php echo __('name'); ?></label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($currentUser['name']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="email"><?php echo __('email'); ?></label>
                        <input type="email" id="email" value="<?php echo htmlspecialchars($currentUser['email']); ?>" disabled>
                        <small style="color: var(--text-secondary);"><?php echo __('email_cannot_change'); ?></small>
                    </div>

                    <div class="form-group">
                        <label for="locale"><?php echo __('language'); ?></label>
                        <select id="locale" name="locale">
                            <option value="ckb" <?php echo ($currentUser['locale'] ?? 'ckb') === 'ckb' ? 'selected' : ''; ?>><?php echo __('kurdish'); ?></option>
                            <option value="en" <?php echo ($currentUser['locale'] ?? 'ckb') === 'en' ? 'selected' : ''; ?>><?php echo __('english'); ?></option>
                            <option value="ar" <?php echo ($currentUser['locale'] ?? 'ckb') === 'ar' ? 'selected' : ''; ?>><?php echo __('arabic'); ?></option>
                        </select>
                    </div>

                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i> <?php echo __('save'); ?>
                    </button>
                </form>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i> <?php echo __('back_to_home'); ?>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Initialize dark mode on page load
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.body.classList.add('dark-mode');
                document.documentElement.classList.add('dark-mode');
            }
        });
    </script>
</body>
</html>

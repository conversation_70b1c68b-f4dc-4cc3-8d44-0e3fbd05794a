<?php
// Add word API endpoint
require_once '../config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('تەنها داواکاری POST قبوولە');
    }

    $word = isset($_POST['word']) ? trim($_POST['word']) : '';
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $difficulty = isset($_POST['difficulty']) ? $_POST['difficulty'] : 'medium';

    if (empty($word)) {
        throw new Exception('وشە پێویستە');
    }

    // Check if word already exists
    $existing = fetchRow("SELECT id FROM words WHERE word = ?", [$word]);
    if ($existing) {
        throw new Exception('ئەم وشەیە پێشتر زیادکراوە');
    }

    // Validate difficulty
    if (!in_array($difficulty, ['easy', 'medium', 'hard'])) {
        $difficulty = 'medium';
    }

    $conn = getDBConnection();

    // Insert word
    $stmt = $conn->prepare("INSERT INTO words (word, description, difficulty_level) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $word, $description, $difficulty);

    if (!$stmt->execute()) {
        throw new Exception('هەڵەیەک ڕوویدا لە زیادکردنی وشە');
    }

    $wordId = $conn->insert_id;
    $stmt->close();

    // Handle video upload if provided
    $videoPath = null;
    if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
        $videoPath = handleVideoUpload($_FILES['video'], $wordId);

        if ($videoPath) {
            // Update word with video path
            $updateStmt = $conn->prepare("UPDATE words SET video_path = ? WHERE id = ?");
            $updateStmt->bind_param("si", $videoPath, $wordId);
            $updateStmt->execute();
            $updateStmt->close();
        }
    }

    $conn->close();

    echo json_encode([
        'success' => true,
        'message' => 'وشە بە سەرکەوتوویی زیادکرا',
        'word_id' => $wordId,
        'video_path' => $videoPath
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function handleVideoUpload($file, $wordId) {
    try {
        // Validate file
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('قەبارەی فایل زۆر گەورەیە');
        }

        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ALLOWED_VIDEO_TYPES)) {
            throw new Exception('جۆری فایل پشتگیری ناکرێت');
        }

        // Generate unique filename
        $filename = 'word_' . $wordId . '_' . time() . '.' . $fileExtension;
        $uploadPath = UPLOAD_DIR . $filename;
        $webPath = UPLOAD_URL . $filename;

        // Create upload directory if it doesn't exist
        if (!file_exists(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0777, true);
        }

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            return $webPath; // Return web path instead of file path
        } else {
            throw new Exception('هەڵەیەک ڕوویدا لە بارکردنی فایل');
        }

    } catch (Exception $e) {
        error_log("Video upload error: " . $e->getMessage());
        return null;
    }
}
?>

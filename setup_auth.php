<?php
/**
 * Setup script for Google OAuth Authentication
 * This script helps configure the authentication system
 */

require_once 'config.php';

// Check if this is a POST request to update database
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        if ($_POST['action'] === 'update_database') {
            updateDatabaseSchema();
            echo json_encode(['success' => true, 'message' => 'Database updated successfully!']);
        } elseif ($_POST['action'] === 'migrate_data') {
            migrateExistingData();
            echo json_encode(['success' => true, 'message' => 'Data migrated successfully!']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

function updateDatabaseSchema() {
    $conn = getDBConnection();
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows == 0) {
        // Create users table
        $sql = "CREATE TABLE `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `google_id` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `name` varchar(255) NOT NULL,
            `picture` varchar(500) DEFAULT NULL,
            `locale` varchar(10) DEFAULT 'en',
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            `last_login` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_google_id` (`google_id`),
            UNIQUE KEY `unique_email` (`email`),
            KEY `idx_users_email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        if (!$conn->query($sql)) {
            throw new Exception("Error creating users table: " . $conn->error);
        }
    }
    
    // Check if user_id column exists in daily_progress
    $result = $conn->query("SHOW COLUMNS FROM daily_progress LIKE 'user_id'");
    if ($result->num_rows == 0) {
        // Add user_id column
        $sql = "ALTER TABLE daily_progress ADD COLUMN user_id int(11) NOT NULL AFTER id";
        if (!$conn->query($sql)) {
            throw new Exception("Error adding user_id to daily_progress: " . $conn->error);
        }
        
        // Update unique constraint
        $conn->query("ALTER TABLE daily_progress DROP INDEX unique_date");
        $sql = "ALTER TABLE daily_progress ADD UNIQUE KEY unique_user_date (user_id, study_date)";
        if (!$conn->query($sql)) {
            throw new Exception("Error updating daily_progress constraints: " . $conn->error);
        }
        
        // Add foreign key
        $sql = "ALTER TABLE daily_progress ADD CONSTRAINT daily_progress_ibfk_1 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE";
        $conn->query($sql); // Don't throw error if constraint already exists
    }
    
    // Check if user_id column exists in study_sessions
    $result = $conn->query("SHOW COLUMNS FROM study_sessions LIKE 'user_id'");
    if ($result->num_rows == 0) {
        // Add user_id column
        $sql = "ALTER TABLE study_sessions ADD COLUMN user_id int(11) NOT NULL AFTER id";
        if (!$conn->query($sql)) {
            throw new Exception("Error adding user_id to study_sessions: " . $conn->error);
        }
        
        // Add foreign key
        $sql = "ALTER TABLE study_sessions ADD CONSTRAINT study_sessions_ibfk_2 FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE";
        $conn->query($sql); // Don't throw error if constraint already exists
    }
    
    $conn->close();
}

function migrateExistingData() {
    $conn = getDBConnection();
    
    // Create a default user for existing data
    $defaultEmail = '<EMAIL>';
    $defaultName = 'مەڵبەندی سەرەکی';
    $defaultGoogleId = 'local_admin_' . time();
    
    // Check if default user exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $defaultEmail);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        // Create default user
        $stmt = $conn->prepare("INSERT INTO users (google_id, email, name) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $defaultGoogleId, $defaultEmail, $defaultName);
        if (!$stmt->execute()) {
            throw new Exception("Error creating default user: " . $conn->error);
        }
        $userId = $conn->insert_id;
    } else {
        $user = $result->fetch_assoc();
        $userId = $user['id'];
    }
    
    // Update daily_progress records without user_id
    $stmt = $conn->prepare("UPDATE daily_progress SET user_id = ? WHERE user_id = 0 OR user_id IS NULL");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    
    // Update study_sessions records without user_id
    $stmt = $conn->prepare("UPDATE study_sessions SET user_id = ? WHERE user_id = 0 OR user_id IS NULL");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    
    $conn->close();
}

// Check current configuration
$configStatus = [
    'google_client_id' => defined('GOOGLE_CLIENT_ID') && GOOGLE_CLIENT_ID !== 'YOUR_GOOGLE_CLIENT_ID_HERE',
    'google_client_secret' => defined('GOOGLE_CLIENT_SECRET') && GOOGLE_CLIENT_SECRET !== 'YOUR_GOOGLE_CLIENT_SECRET_HERE',
    'database_connection' => false,
    'users_table_exists' => false,
    'schema_updated' => false
];

try {
    $conn = getDBConnection();
    $configStatus['database_connection'] = true;
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    $configStatus['users_table_exists'] = $result->num_rows > 0;
    
    // Check if schema is updated
    $result = $conn->query("SHOW COLUMNS FROM daily_progress LIKE 'user_id'");
    $dailyProgressUpdated = $result->num_rows > 0;
    
    $result = $conn->query("SHOW COLUMNS FROM study_sessions LIKE 'user_id'");
    $studySessionsUpdated = $result->num_rows > 0;
    
    $configStatus['schema_updated'] = $dailyProgressUpdated && $studySessionsUpdated;
    
    $conn->close();
} catch (Exception $e) {
    // Database connection failed
}
?>

<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ڕێکخستنی سیستەمی چوونەژوورەوە - دەستەڕێ</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .status-item.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-item.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .code-block {
            background: #f1f5f9;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <h1><i class="fas fa-cog"></i> ڕێکخستنی سیستەمی چوونەژوورەوە</h1>
            
            <div class="setup-section">
                <h2>١. پشکنینی ڕێکخستن</h2>
                
                <div class="status-item <?php echo $configStatus['database_connection'] ? 'success' : 'error'; ?>">
                    <span><i class="fas fa-database"></i> پەیوەندی بنکەی زانیاری</span>
                    <span><?php echo $configStatus['database_connection'] ? '✓ سەرکەوتوو' : '✗ شکستخواردوو'; ?></span>
                </div>
                
                <div class="status-item <?php echo $configStatus['google_client_id'] ? 'success' : 'error'; ?>">
                    <span><i class="fab fa-google"></i> Google Client ID</span>
                    <span><?php echo $configStatus['google_client_id'] ? '✓ ڕێکخراوە' : '✗ ڕێکنەخراوە'; ?></span>
                </div>
                
                <div class="status-item <?php echo $configStatus['google_client_secret'] ? 'success' : 'error'; ?>">
                    <span><i class="fas fa-key"></i> Google Client Secret</span>
                    <span><?php echo $configStatus['google_client_secret'] ? '✓ ڕێکخراوە' : '✗ ڕێکنەخراوە'; ?></span>
                </div>
                
                <div class="status-item <?php echo $configStatus['users_table_exists'] ? 'success' : 'error'; ?>">
                    <span><i class="fas fa-table"></i> خشتەی بەکارهێنەران</span>
                    <span><?php echo $configStatus['users_table_exists'] ? '✓ دروستکراوە' : '✗ دروستنەکراوە'; ?></span>
                </div>
                
                <div class="status-item <?php echo $configStatus['schema_updated'] ? 'success' : 'error'; ?>">
                    <span><i class="fas fa-database"></i> نوێکردنەوەی بنکەی زانیاری</span>
                    <span><?php echo $configStatus['schema_updated'] ? '✓ نوێکراوەتەوە' : '✗ نوێنەکراوەتەوە'; ?></span>
                </div>
            </div>
            
            <?php if (!$configStatus['google_client_id'] || !$configStatus['google_client_secret']): ?>
            <div class="setup-section">
                <h2>٢. ڕێکخستنی Google OAuth</h2>
                <p>بۆ ڕێکخستنی چوونەژوورەوە بە گووگڵ، ئەم هەنگاوانە بکە:</p>
                
                <ol>
                    <li>بڕۆ بۆ <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                    <li>پڕۆژەیەکی نوێ دروست بکە یان یەکێکی هەبوو هەڵبژێرە</li>
                    <li>چالاک بکە: Google+ API</li>
                    <li>OAuth 2.0 credentials دروست بکە (Web application)</li>
                    <li>ئەم URLانە زیاد بکە بۆ Authorized redirect URIs:</li>
                </ol>
                
                <div class="code-block">
                    http://localhost/dastare/auth/google_callback.php<br>
                    http://yourdomain.com/dastare/auth/google_callback.php
                </div>
                
                <p>پاشان Client ID و Client Secret لە فایلی <code>auth_config.php</code> نوێ بکەرەوە:</p>
                
                <div class="code-block">
                    define('GOOGLE_CLIENT_ID', 'your_actual_client_id_here');<br>
                    define('GOOGLE_CLIENT_SECRET', 'your_actual_client_secret_here');
                </div>
            </div>
            <?php endif; ?>
            
            <?php if ($configStatus['database_connection'] && !$configStatus['schema_updated']): ?>
            <div class="setup-section">
                <h2>٣. نوێکردنەوەی بنکەی زانیاری</h2>
                <p>بۆ زیادکردنی پشتگیری بەکارهێنەران، پێویستە بنکەی زانیاری نوێ بکرێتەوە:</p>
                
                <button class="btn btn-primary" onclick="updateDatabase()">
                    <i class="fas fa-database"></i>
                    نوێکردنەوەی بنکەی زانیاری
                </button>
                
                <?php if ($configStatus['users_table_exists']): ?>
                <button class="btn btn-success" onclick="migrateData()" style="margin-right: 1rem;">
                    <i class="fas fa-exchange-alt"></i>
                    گواستنەوەی زانیارییە هەبووەکان
                </button>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <?php if ($configStatus['google_client_id'] && $configStatus['google_client_secret'] && $configStatus['schema_updated']): ?>
            <div class="setup-section">
                <h2>✅ ڕێکخستن تەواو بوو!</h2>
                <p>سیستەمی چوونەژوورەوە ئامادەیە. ئێستا دەتوانیت:</p>
                
                <a href="auth/login.php" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    چوونەژوورەوە
                </a>
                
                <a href="index.php" class="btn btn-success" style="margin-right: 1rem;">
                    <i class="fas fa-home"></i>
                    گەڕانەوە بۆ سەرەکی
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        async function updateDatabase() {
            try {
                const response = await fetch('setup_auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=update_database'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('بنکەی زانیاری بە سەرکەوتوویی نوێکرایەوە!');
                    location.reload();
                } else {
                    alert('هەڵە: ' + result.message);
                }
            } catch (error) {
                alert('هەڵەیەک ڕوویدا: ' + error.message);
            }
        }
        
        async function migrateData() {
            if (!confirm('ئایا دڵنیایت لە گواستنەوەی زانیارییە هەبووەکان؟')) {
                return;
            }
            
            try {
                const response = await fetch('setup_auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=migrate_data'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('زانیاری بە سەرکەوتوویی گواسترایەوە!');
                    location.reload();
                } else {
                    alert('هەڵە: ' + result.message);
                }
            } catch (error) {
                alert('هەڵەیەک ڕوویدا: ' + error.message);
            }
        }
    </script>
</body>
</html>
